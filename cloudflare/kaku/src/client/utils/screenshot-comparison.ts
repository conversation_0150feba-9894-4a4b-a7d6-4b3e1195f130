/**
 * Screenshot Comparison Utilities - Image comparison and analysis
 *
 * This module provides utilities for screenshot comparison functionality.
 * It offers methods to compare images and calculate difference percentages
 * using the pixelmatch library for accurate pixel-level comparison.
 *
 * ## Features
 * - Pixel-level image comparison with configurable thresholds
 * - Sampling support for performance optimization
 * - Anti-aliasing detection and handling
 * - Diff mask generation for visual debugging
 * - Test image creation utilities
 *
 * ## Usage
 * ```typescript
 * // Compare two images
 * const result = window.screenshotComparison.compareScreenshots(img1, img2, {
 *   threshold: 0.01,
 *   sampling: 2
 * });
 *
 * // Create test image
 * const testImage = window.screenshotComparison.createTestImage(640, 480, [255, 0, 0, 255]);
 * ```
 */

import type {
  ScreenshotComparisonOptions,
  ImageData as IImageData,
  ComparisonResult,
} from '../../common/types/common';
import { ScreenshotComparisonUtils } from '../types';

// Reusable diff buffer to avoid creating a new one for each comparison
let diffBuffer: Uint8Array | null = null;

// Define the utilities in the global scope so they can be exported
const screenshotComparisonUtils = (function (): ScreenshotComparisonUtils {
  /**
   * Compares two image data objects to calculate the percentage of different pixels.
   * Uses pixelmatch library to perform the comparison.
   * This optimized version works directly with the data arrays and reuses the diff buffer.
   */
  function compareScreenshots(
    img1: IImageData,
    img2: IImageData,
    options: ScreenshotComparisonOptions = {},
  ): ComparisonResult {
    const startTime = performance.now();

    if (!img1 || !img2) {
      console.error('Unable to compare screenshots: missing image data');
      return { percentageDiff: 0, numDiffPixels: 0, comparisonTime: 0 };
    }

    if (typeof window.pixelmatch !== 'function') {
      console.error('pixelmatch function not available. Ensure dependency is loaded.');
      return { percentageDiff: 0, numDiffPixels: 0, comparisonTime: 0 };
    }

    if (img1.width !== img2.width || img1.height !== img2.height) {
      console.error(
        `Image dimensions do not match: img1(${img1.width}x${img1.height}) vs img2(${img2.width}x${img2.height})`,
      );
      return { percentageDiff: 0, numDiffPixels: 0, comparisonTime: 0 };
    }

    // Get sampling rate (default to 1 = check every pixel)
    const sampling = options.sampling && options.sampling > 1 ? Math.floor(options.sampling) : 1;

    try {
      if (sampling > 1) {
        const sampledWidth = Math.ceil(img1.width / sampling);
        const sampledHeight = Math.ceil(img1.height / sampling);
        const sampledTotalPixels = sampledWidth * sampledHeight * 4;

        const sampledData1 = new Uint8ClampedArray(sampledTotalPixels);
        const sampledData2 = new Uint8ClampedArray(sampledTotalPixels);

        if (!diffBuffer || diffBuffer.length !== sampledTotalPixels) {
          diffBuffer = new Uint8Array(sampledTotalPixels);
        }

        let sampledIndex = 0;
        for (let y = 0; y < img1.height; y += sampling) {
          for (let x = 0; x < img1.width; x += sampling) {
            const originalIndex = (y * img1.width + x) * 4;
            const targetIndex = sampledIndex * 4;

            for (let i = 0; i < 4; i++) {
              sampledData1[targetIndex + i] = img1.data[originalIndex + i];
              sampledData2[targetIndex + i] = img2.data[originalIndex + i];
            }

            sampledIndex++;
          }
        }

        // Create sampled image objects
        const sampledImg1: IImageData = {
          data: sampledData1,
          width: sampledWidth,
          height: sampledHeight,
        };
        const sampledImg2: IImageData = {
          data: sampledData2,
          width: sampledWidth,
          height: sampledHeight,
        };

        const pixelmatchOptions = {
          threshold: options.threshold !== undefined ? options.threshold : 0.01,
          includeAA: options.includeAA !== undefined ? options.includeAA : false,
          diffMask: options.diffMask !== undefined ? options.diffMask : false,
        };

        const numDiffPixels = window.pixelmatch(
          sampledImg1.data,
          sampledImg2.data,
          diffBuffer,
          sampledWidth,
          sampledHeight,
          pixelmatchOptions,
        );

        const totalSampledPixels = sampledWidth * sampledHeight;
        const percentageDiff =
          totalSampledPixels > 0 ? (numDiffPixels / totalSampledPixels) * 100 : 0;

        // Scale the number of different pixels to the original image size for consistency
        const scaledDiffPixels = Math.round(numDiffPixels * (sampling * sampling));

        const comparisonTime = performance.now() - startTime;

        return {
          percentageDiff,
          numDiffPixels: scaledDiffPixels,
          comparisonTime,
        };
      } else {
        // Full resolution comparison
        const totalPixels = img1.width * img1.height * 4;

        if (!diffBuffer || diffBuffer.length !== totalPixels) {
          diffBuffer = new Uint8Array(totalPixels);
        }

        const pixelmatchOptions = {
          threshold: options.threshold !== undefined ? options.threshold : 0.01,
          includeAA: options.includeAA !== undefined ? options.includeAA : false,
          diffMask: options.diffMask !== undefined ? options.diffMask : false,
        };

        const numDiffPixels = window.pixelmatch(
          img1.data,
          img2.data,
          diffBuffer,
          img1.width,
          img1.height,
          pixelmatchOptions,
        );

        const totalPixelsCount = img1.width * img1.height;
        const percentageDiff = totalPixelsCount > 0 ? (numDiffPixels / totalPixelsCount) * 100 : 0;

        const comparisonTime = performance.now() - startTime;

        return {
          percentageDiff,
          numDiffPixels,
          comparisonTime,
        };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('Error during screenshot comparison:', errorMessage);

      const comparisonTime = performance.now() - startTime;
      return { percentageDiff: 0, numDiffPixels: 0, comparisonTime };
    }
  }

  /**
   * Prepares a buffer for comparison by converting it to ImageData format.
   * This is useful when working with raw RGBA buffers from various sources.
   */
  function prepareBufferForComparison(
    buffer: Uint8Array,
    width: number,
    height: number,
  ): IImageData {
    const expectedLength = width * height * 4;

    if (buffer.length !== expectedLength) {
      throw new Error(
        `Buffer length mismatch: expected ${expectedLength} bytes for ${width}x${height} image, got ${buffer.length}`,
      );
    }

    return {
      data: new Uint8ClampedArray(buffer),
      width,
      height,
    };
  }

  /**
   * Creates a test image with a solid color.
   * Useful for testing screenshot comparison functionality.
   */
  function createTestImage(
    width: number,
    height: number,
    color: number[] = [128, 128, 128, 255],
  ): IImageData {
    const totalPixels = width * height;
    const data = new Uint8ClampedArray(totalPixels * 4);

    for (let i = 0; i < totalPixels; i++) {
      const offset = i * 4;
      data[offset] = color[0]; // R
      data[offset + 1] = color[1]; // G
      data[offset + 2] = color[2]; // B
      data[offset + 3] = color[3]; // A
    }

    return {
      data,
      width,
      height,
    };
  }

  /**
   * Creates a test image with a specified number of different pixels.
   * Useful for testing the screenshot comparison with controlled differences.
   */
  function createImageWithDifferences(
    baseImage: IImageData,
    diffPixelCount: number,
    diffColor: number[] = [0, 0, 255, 255],
  ): IImageData {
    const { width, height, data } = baseImage;
    const newData = new Uint8ClampedArray(data);
    const totalPixels = width * height;

    const pixelsToModify = Math.min(diffPixelCount, totalPixels);

    const pixelIndices = new Set<number>();
    while (pixelIndices.size < pixelsToModify) {
      pixelIndices.add(Math.floor(Math.random() * totalPixels));
    }

    for (const pixelIndex of pixelIndices) {
      const offset = pixelIndex * 4;
      newData[offset] = diffColor[0]; // R
      newData[offset + 1] = diffColor[1]; // G
      newData[offset + 2] = diffColor[2]; // B
      newData[offset + 3] = diffColor[3]; // A
    }

    return {
      data: newData,
      width,
      height,
    };
  }

  const utils: ScreenshotComparisonUtils = {
    compareScreenshots,
    prepareBufferForComparison,
    createTestImage,
    createImageWithDifferences,
  };

  return utils;
})();

export default screenshotComparisonUtils;

// Attach to window for global access
if (typeof window !== 'undefined') {
  window.screenshotComparisonUtils = screenshotComparisonUtils;
}
