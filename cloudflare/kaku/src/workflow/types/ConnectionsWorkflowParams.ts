import { Action } from '../../agent/types/extract-result';
import { PlatformTypes } from "../../ui/constants";
import { ElementCoordinateMapping } from '../../agent/services/coordinate-resolution';

export type ConnectionsWorkflowParams = {
  platformId: PlatformTypes;
  userId: string;
  sessionId: string;
  linkId: string;
  workflowId: string;
};

export type FormSubmissionEventPayload = {
  actions: Action[];
};

export type FormSubmissionEvent = {
  payload: string;
  coordinates?: ElementCoordinateMapping;
  source: FormSubmissionPayloadSource
};

export enum FormSubmissionPayloadSource {
  PAGE_FORM_SUBMISSION,
  TWO_FACTOR_AUTHENTICATION_COMPLETION
}