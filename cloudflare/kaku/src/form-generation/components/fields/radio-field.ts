/**
 * Radio field component
 */

import type { FormField } from '../../types';

/**
 * Generate radio field markup
 */
export function generateRadioField(field: FormField): string {
  if (!field.options?.length) {
    return `
      <div class="input-container">
        <div class="radio-group">
          <input
            class="radio-input"
            type="radio"
            id="${field.id}"
            name="${field.id}"
          >
          <label class="radio-label" for="${field.id}">
            <span class="radio-button"></span>
            <span class="radio-text">${field.label}</span>
          </label>
        </div>
      </div>
    `;
  }

  const radioOptions = field.options
    .map(
      (option) => `
        <div class="radio-group">
          <input
            class="radio-input"
            type="radio"
            id="${field.id}-${option.value}"
            name="${field.id}"
            value="${option.value}"
          >
          <label class="radio-label" for="${field.id}-${option.value}">
            <span class="radio-button"></span>
            <span class="radio-text">${option.label}</span>
          </label>
        </div>
      `,
    )
    .join('');

  return `
    <div class="input-container">
      <div class="radio-fieldset">
        <div class="radio-legend">${field.label}</div>
        <div class="radio-options">
          ${radioOptions}
        </div>
      </div>
    </div>
  `;
}
