import { Action } from '../types/extract-result';
import { FormSubmissionPayload } from '../types/form-types';
import { ElementCoordinateMapping } from '../services/coordinate-resolution';

/**
 * Form Handler Module
 * Handles all form submission related functionality including validation,
 * action creation, and workflow communication
 * Uses function-level callbacks instead of constructor injection
 */
export class FormHandler {
  private config = {
    debug: false,
  };

  private log(...args: any[]): void {
    if (this.config.debug) {
      console.log('[kazeel][form-handler]', ...args);
    }
  }

  /**
   * Main form submission handler that orchestrates the entire process
   * Uses callback parameters instead of constructor injection
   */
  async validatePayloadAndPrepareActions(
    payload: FormSubmissionPayload,
    elementCoordinateMapping: ElementCoordinateMapping | undefined,
    pageStateResult: any,
    callbacks: {
      onFormSubmissionError: (error: unknown) => void;
    },
  ): Promise<Action[] | undefined> {
    try {
      this.validatePayload(payload);

      this.log(
        `[FORM SUBMISSION] Type: ${payload.interaction}, Clicked Element: ${payload.clickId}`,
      );

      const actions = this.createFormActions(payload, elementCoordinateMapping, pageStateResult);

      this.validateActions(actions);

      actions.sort((a, b) => a.order - b.order);
      return actions;
    } catch (error) {
      this.log('Failed to handle form submission:', error);
      callbacks.onFormSubmissionError(error);
    }
  }

  /**
   * Validate the form submission payload
   */
  private validatePayload(payload: FormSubmissionPayload): void {
    const { clickId, interaction } = payload;

    if (!clickId) {
      throw new Error('Missing required field: clickId');
    }
    if (!interaction) {
      throw new Error('Missing required field: interaction');
    }
  }

  /**
   * Create actions based on form submission payload
   */
  private createFormActions(
    payload: FormSubmissionPayload,
    elementCoordinateMapping: ElementCoordinateMapping | undefined,
    pageStateResult: any,
  ): Action[] {
    const { clickId, interaction, ...formValues } = payload;
    const actions: Action[] = [];

    if (interaction === 'submit') {
      this.addFormFieldActions(formValues, actions, elementCoordinateMapping, pageStateResult);
      this.addSubmitAction(clickId, actions, elementCoordinateMapping, pageStateResult);
    } else if (interaction === 'click') {
      this.addClickAction(clickId, actions, elementCoordinateMapping, pageStateResult);
    }

    return actions;
  }

  /**
   * Add form field actions (fill and select) to the actions array
   */
  private addFormFieldActions(
    formValues: Record<string, string>,
    actions: Action[],
    elementCoordinateMapping: ElementCoordinateMapping | undefined,
    pageStateResult: any,
  ): void {
    Object.entries(formValues).forEach(([fieldId, value]) => {
      if (!value) return;

      // Check if this is a radio button selection (fieldId-optionValue pattern)
      const radioActionName = `${fieldId}-${value}`;
      if (elementCoordinateMapping?.[radioActionName]) {
        const fieldOrder = this.getFieldOrder(radioActionName, pageStateResult);
        actions.push({
          type: 'select',
          name: radioActionName,
          value,
          coordinates: elementCoordinateMapping[radioActionName],
          order: fieldOrder,
          isSubmitAction: false,
        });
      } else if (elementCoordinateMapping?.[fieldId]) {
        const fieldOrder = this.getFieldOrder(fieldId, pageStateResult);
        actions.push({
          type: 'fill',
          name: fieldId,
          value,
          coordinates: elementCoordinateMapping[fieldId],
          order: fieldOrder,
          isSubmitAction: false,
        });
      }
    });
  }

  /**
   * Add submit action to the actions array
   */
  private addSubmitAction(
    clickId: string,
    actions: Action[],
    elementCoordinateMapping: ElementCoordinateMapping | undefined,
    pageStateResult: any,
  ): void {
    if (elementCoordinateMapping?.[clickId]) {
      const submitOrder = this.getFieldOrder(clickId, pageStateResult);
      actions.push({
        type: 'click',
        name: clickId,
        coordinates: elementCoordinateMapping[clickId],
        order: submitOrder,
        isSubmitAction: true,
      });
    }
  }

  /**
   * Add click action to the actions array
   */
  private addClickAction(
    clickId: string,
    actions: Action[],
    elementCoordinateMapping: ElementCoordinateMapping | undefined,
    pageStateResult: any,
  ): void {
    if (elementCoordinateMapping?.[clickId]) {
      const clickOrder = this.getFieldOrder(clickId, pageStateResult);
      actions.push({
        type: 'click',
        name: clickId,
        coordinates: elementCoordinateMapping[clickId],
        order: clickOrder,
        isSubmitAction: false,
      });
    }
  }

  /**
   * Validate that all actions have required coordinates
   */
  private validateActions(actions: Action[]): void {
    const actionsWithMissingCoordinates = actions.filter((action) => !action.coordinates);
    if (actionsWithMissingCoordinates.length > 0) {
      const missingActionNames = actionsWithMissingCoordinates
        .map((action) => action.name)
        .join(', ');
      throw new Error(`Actions missing coordinates: ${missingActionNames}`);
    }
  }

  /**
   * Helper method to get field order from extraction result
   */
  private getFieldOrder(elementId: string, pageStateResult: any): number {
    const { fields, buttons } = pageStateResult.extractionResult.controls;

    const field = fields.find((f: any) => f.id === elementId || elementId.startsWith(`${f.id}-`));
    if (field) {
      return field.order;
    }

    const button = buttons.find((b: any) => b.id === elementId);
    if (button) {
      return button.order;
    }

    return 999;
  }
}
