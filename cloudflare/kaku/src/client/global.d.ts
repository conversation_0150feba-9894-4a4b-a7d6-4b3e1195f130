/**
 * Global TypeScript declarations for browser client scripts
 *
 * This file extends the global Window interface to include all browser controller objects
 * that are injected into browser contexts. It provides type safety for all window-level
 * object interactions throughout the codebase.
 *
 * ## Usage
 * This file is automatically included in TypeScript compilation and provides types for:
 * - window.browserController
 * - window.screenCropper
 * - window.captchaDetector
 * - window.tfCaptchaDetector
 * - window.persistentCDPController
 * - window.crossTabCommunicator
 *
 * ## Benefits
 * - **Type Safety**: Full TypeScript support for all window object interactions
 * - **IntelliSense**: Auto-completion and method discovery in IDEs
 * - **Error Prevention**: Compile-time checking for method signatures and return types
 * - **Maintainability**: Centralized type definitions for all client scripts
 *
 * @example
 * ```typescript
 * // Now fully typed with IntelliSense support
 * await window.browserController.init();
 * const screenshot = await window.browserController.takeScreenshot();
 * await window.screenCropper.start(viewport);
 * ```
 */

import type {
  <PERSON>rowser<PERSON>ontroller,
  ScreenCropperClient,
  CaptchaDetectorClient,
  TensorFlowCaptchaDete<PERSON>,
  PersistentCDPController,
  CrossTabCommunicator,
  ScreenshotComparisonUtils,
} from './types/index';

declare global {
  class MediaStreamTrackProcessor {
    constructor(init: { track: MediaStreamTrack });
    readonly readable: ReadableStream<VideoFrame>;
  }

  class MediaStreamTrackGenerator {
    constructor(init: { kind: string });
    readonly writable: WritableStream<VideoFrame>;
  }
  interface Window {
    /**
     * Browser Controller - handles CDP operations and user interactions
     * Available in target tabs (user interaction tabs)
     */
    browserController: BrowserController;

    /**
     * Screen Cropper - handles WebRTC streaming and screen capture
     * Available in target tabs for video streaming operations
     */
    screenCropper: ScreenCropperClient;

    /**
     * Captcha Detector - handles screenshot comparison and UI change detection
     * Available in target tabs for visual change monitoring
     */
    captchaDetector: CaptchaDetectorClient;

    /**
     * TensorFlow Captcha Detector - handles ML-based captcha detection
     * Available in target tabs for AI-powered captcha detection
     */
    tfCaptchaDetector: TensorFlowCaptchaDetector;

    /**
     * Persistent CDP Controller - handles CDP operations in control tabs
     * Available in control tabs with active CDP connections
     */
    persistentCDPController: PersistentCDPController;

    /**
     * Cross Tab Communicator - handles communication between tabs
     * Available in both control and target tabs for inter-tab messaging
     */
    crossTabCommunicator: CrossTabCommunicator;
    screenshotComparisonUtils: ScreenshotComparisonUtils;
    pixelmatch: any;
    socket: WebSocket | null;
  }
}

// Export empty object to make this a module
export {};
