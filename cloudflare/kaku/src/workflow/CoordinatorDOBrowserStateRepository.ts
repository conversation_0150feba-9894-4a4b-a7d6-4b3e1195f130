import { BrowserStateRepository } from './types/BrowserStateRepository';
import { BrowserState } from './types/BrowserState';
import { CoordinatorDO } from '../coordinator';
import { UserSessionData } from '../shared/coordinator-types';
import { PlatformTypes } from '../ui/constants';

export class CoordinatorDOBrowserStateRepository implements BrowserStateRepository {
  private coordinatorDO: DurableObjectNamespace<CoordinatorDO>;
  constructor(coordinatorDO: DurableObjectNamespace<CoordinatorDO>) {
    this.coordinatorDO = coordinatorDO;
  }
  async deleteBrowserState(userId: string, platform: PlatformTypes): Promise<void> {
    const coordinatorId = this.coordinatorDO.idFromName(userId);
    const coordinatorStub = this.coordinatorDO.get(coordinatorId);

    await coordinatorStub.clearSessionData(platform);
  }

  async getBrowserState(userId: string, platform: PlatformTypes): Promise<BrowserState | null> {
    const coordinatorId = this.coordinatorDO.idFromName(userId);
    const coordinatorStub = this.coordinatorDO.get(coordinatorId);

    const userSessionData = await coordinatorStub.getSessionData(platform);
    if (!userSessionData || !userSessionData.browserState) {
      return null;
    }
    return {
      platform: platform,
      userId: userId,
      cookies: userSessionData.browserState.cookies,
      localStorageData: userSessionData.browserState.localStorageData,
      sessionStorageData: userSessionData.browserState.sessionStorageData,
      lastUpdated: userSessionData.lastUpdated,
    };
  }

  async updateBrowserState(browserState: BrowserState): Promise<BrowserState> {
    const coordinatorId = this.coordinatorDO.idFromName(browserState.userId);
    const coordinatorStub = this.coordinatorDO.get(coordinatorId);
    await coordinatorStub.updateSessionData(
      browserState.platform,
      this.generateUserSessionBrowserState(browserState),
    );

    return browserState;
  }

  private generateUserSessionBrowserState(browserState: BrowserState): UserSessionData {
    return {
      browserState: {
        cookies: browserState.cookies,
        localStorageData: browserState.localStorageData,
        sessionStorageData: browserState.sessionStorageData,
      },
      lastUpdated: browserState.lastUpdated,
    };
  }
}
