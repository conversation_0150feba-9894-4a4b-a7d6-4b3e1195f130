/* Custom styles for PlainSheet to integrate with our card design */
.ps-sheet {
  border-radius: 30px 30px 15px 15px !important;
  overflow: hidden !important;
  width: 435px !important;
  max-width: 100% !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  margin-top: 0 !important;
  top: auto !important;
  bottom: auto !important;
  position: absolute !important;
}

.ps-content {
  background-color: #fff !important;
  border-radius: 30px 30px 15px 15px !important;
}

.ps-overlay {
  background-color: rgba(0, 0, 0, 0.4) !important;
}

/* Fix for mobile view */
@media (max-width: 768px) {
  .ps-sheet {
    width: 100% !important;
    max-width: 100% !important;
    border-radius: 15px 15px 0 0 !important;
  }

  .ps-content {
    border-radius: 15px 15px 0 0 !important;
  }
}

/* Custom class for positioning */
.kazeel-bottom-sheet {
  position: absolute !important;
  top: auto !important;
  bottom: 0 !important;
}

/* Custom bottom sheet styles */
.bottom-sheet-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 1000;
  display: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.bottom-sheet {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 435px;
  max-width: 100%;
  background-color: white;
  border-radius: 30px 30px 15px 15px;
  overflow: hidden;
  z-index: 1001;
  display: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.bottom-sheet-handle {
  width: 40px;
  height: 5px;
  background-color: #e0e0e0;
  border-radius: 3px;
  margin: 10px auto;
}

@media (max-width: 768px) {
  .bottom-sheet {
    width: 100%;
    border-radius: 15px 15px 0 0;
  }
}

#help-trigger {
  margin-bottom: 20px;
}

.bottom-sheet.active,
.bottom-sheet-overlay.active {
  display: block;
  opacity: 1;
}
