import { argon2id, argon2Verify, setWASMModules } from 'argon2-wasm-edge';
// @ts-expect-error
import argon2WASM from 'argon2-wasm-edge/wasm/argon2.wasm';
// @ts-expect-error
import blake2bWASM from 'argon2-wasm-edge/wasm/blake2b.wasm';

// Initialize WASM modules (required for Cloudflare Workers)
setWASMModules({ argon2WASM, blake2bWASM });

interface PasswordEncoder {
    encode(rawPassword: string): Promise<string>;
    matches(rawPassword: string, encodedPassword: string): Promise<boolean>;
}

class Argon2PasswordEncoder implements PasswordEncoder {
    private readonly parallelism: number;
    private readonly iterations: number;
    private readonly memorySize: number;
    private readonly hashLength: number;
    private readonly saltLength: number;

    constructor(
        parallelism: number = 1,
        iterations: number = 2,
        memorySize: number = 16384, // in kilobytes
        hashLength: number = 32,
        saltLength: number = 16
    ) {
        this.parallelism = parallelism;
        this.iterations = iterations;
        this.memorySize = memorySize;
        this.hashLength = hashLength;
        this.saltLength = saltLength;
    }

    static defaultsForSpringSecurity_v5_8(): Argon2PasswordEncoder {
        return new Argon2PasswordEncoder();
    }

    async encode(rawPassword: string): Promise<string> {
        const salt = new Uint8Array(this.saltLength);
        crypto.getRandomValues(salt);

        return await argon2id({
            password: rawPassword,
            salt,
            parallelism: this.parallelism,
            iterations: this.iterations,
            memorySize: this.memorySize,
            hashLength: this.hashLength,
            outputType: 'encoded'
        });
    }

    async matches(rawPassword: string, encodedPassword: string): Promise<boolean> {
        const prefix = "{argon2@SpringSecurity_v5_8}";
        let hash = encodedPassword;

        if (encodedPassword.startsWith(prefix)) {
            // Remove the prefix to get the actual hash, this is because Sunny adds the prefix
            hash = encodedPassword.substring(prefix.length);
        }
        return await argon2Verify({
            password: rawPassword,
            hash: hash
        });
    }
}

export function passwordEncoder(): PasswordEncoder {
    return Argon2PasswordEncoder.defaultsForSpringSecurity_v5_8();
}