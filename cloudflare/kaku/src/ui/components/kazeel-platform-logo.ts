import { html } from 'hono/html';

/**
 * Kazeel + Platform logo component
 * Provides consistent branding across all UI states with uniform sizing
 */

export type LogoVariant = 'overlapping' | 'connected' | 'separated';

interface KazeelPlatformLogoProps {
  platformLogo?: string;
  platformName?: string;
  variant?: LogoVariant;
  className?: string;
}

/**
 * Consistent size classes for all logo instances
 * Using w-12 h-12 (48px) for uniform appearance across all states
 */
const LOGO_CLASSES = {
  logo: 'w-12 h-12',
  connector: 'w-5 h-3',
};

/**
 * Render overlapping variant (default)
 */
function renderOverlapping(props: KazeelPlatformLogoProps) {
  return html`
    <div class="flex flex-row items-center justify-center ${props.className || ''}">
      <img src="/kazeel-logo.png" alt="Kazeel Logo" class="${LOGO_CLASSES.logo} mt-4 mb-2" />
      ${props.platformLogo
        ? html`
            <img
              src="${props.platformLogo}"
              alt="${props.platformName || 'Platform'} Logo"
              class="${LOGO_CLASSES.logo} mt-4 mb-2 z-[1] -m-2"
            />
          `
        : ''}
    </div>
  `;
}

/**
 * Render connected variant (with link icon)
 */
function renderConnected(props: KazeelPlatformLogoProps) {
  return html`
    <div class="flex flex-row items-center justify-center ${props.className || ''}">
      <img src="/kazeel-logo.png" alt="Kazeel Logo" class="${LOGO_CLASSES.logo} mt-4 mb-2" />
      <img src="/link.png" alt="Connection Icon" class="${LOGO_CLASSES.connector} mt-3 mb-2 mx-4" />
      ${props.platformLogo
        ? html`
            <img
              src="${props.platformLogo}"
              alt="${props.platformName || 'Platform'} Logo"
              class="${LOGO_CLASSES.logo} mt-4 mb-2"
            />
          `
        : ''}
    </div>
  `;
}

/**
 * Render separated variant (with spacing)
 */
function renderSeparated(props: KazeelPlatformLogoProps) {
  return html`
    <div class="flex flex-row items-center justify-center space-x-4 ${props.className || ''}">
      <img src="/kazeel-logo.png" alt="Kazeel Logo" class="${LOGO_CLASSES.logo} mt-4 mb-2" />
      ${props.platformLogo
        ? html`
            <img
              src="${props.platformLogo}"
              alt="${props.platformName || 'Platform'} Logo"
              class="${LOGO_CLASSES.logo} mt-4 mb-2"
            />
          `
        : ''}
    </div>
  `;
}

/**
 * Render shimmer placeholder for loading states
 */
function renderShimmer(className?: string) {
  return html`
    <div class="flex flex-row items-center justify-center ${className || ''}">
      <div class="shimmer ${LOGO_CLASSES.logo} mt-4 mb-2 rounded-full bg-neutral-20"></div>
      <div
        class="shimmer ${LOGO_CLASSES.logo} mt-4 mb-2 z-[1] -m-2 rounded-full bg-neutral-20"
      ></div>
    </div>
  `;
}

export const KazeelPlatformLogo = (props: KazeelPlatformLogoProps = {}) => {
  const { variant = 'overlapping', className } = props;

  if (!props.platformLogo) {
    return renderShimmer(className);
  }

  switch (variant) {
    case 'connected':
      return renderConnected(props);
    case 'separated':
      return renderSeparated(props);
    case 'overlapping':
    default:
      return renderOverlapping(props);
  }
};

export const KazeelPlatformLogoOverlapping = (props: Omit<KazeelPlatformLogoProps, 'variant'>) =>
  KazeelPlatformLogo({ ...props, variant: 'overlapping' });

export const KazeelPlatformLogoConnected = (props: Omit<KazeelPlatformLogoProps, 'variant'>) =>
  KazeelPlatformLogo({ ...props, variant: 'connected' });

export const KazeelPlatformLogoSeparated = (props: Omit<KazeelPlatformLogoProps, 'variant'>) =>
  KazeelPlatformLogo({ ...props, variant: 'separated' });
