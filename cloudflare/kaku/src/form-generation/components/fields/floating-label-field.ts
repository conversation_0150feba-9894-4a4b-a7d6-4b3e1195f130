/**
 * Floating label field component
 */

import type { FormField } from '../../types';

/**
 * Check if field type should use floating label
 */
export function shouldUseFloating<PERSON>abel(type: string): boolean {
  return ['text', 'password', 'email', 'number'].includes(type);
}

/**
 * Generate floating label field markup
 */
export function generateFloating<PERSON>abelField(field: FormField): string {
  const isPasswordField = field.fieldControlType === 'password';

  if (isPasswordField) {
    return generatePasswordFieldWithToggle(field);
  }

  return `
    <div class="input-container">
      <div class="floating-input-wrapper">
        <input
          class="floating-input"
          name="${field.id}"
          type="${field.fieldControlType}"
          placeholder=""
          id="${field.id}"
        >
        <label class="floating-label" for="${field.id}">${field.label}</label>
      </div>
    </div>
  `;
}

/**
 * Generate password field with show/hide toggle
 */
function generatePasswordFieldWithToggle(field: FormField): string {
  const inputId = field.id;
  const toggleId = `${inputId}-toggle`;

  return `
    <div class="input-container">
      <div class="floating-input-wrapper mb-1">
        <input
          class="floating-input"
          name="${field.id}"
          type="password"
          placeholder=""
          id="${field.id}"
        >
        <label class="floating-label" for="${field.id}">${field.label}</label>
      </div>
      <div class="password-toggle-container">
        <input
          type="checkbox"
          id="${toggleId}"
          class="password-toggle-checkbox"
          onchange="togglePasswordVisibility('${inputId}', this)"
        >
        <label for="${toggleId}" class="password-toggle-label">
          <span class="password-toggle-text text-gray-400 text-sm">Show password</span>
        </label>
      </div>
    </div>
  `;
}
