{
  "name": "kaku",
  "main": "src/api/index.ts",
  "compatibility_date": "2025-04-17",
  "compatibility_flags": ["nodejs_compat_v2"],
  "observability": {
    "enabled": true,
  },
  "vars": {
    "ENVIRONMENT": "local",
    "SUNNY_API_ENDPOINT": "http://localhost:8080",
    "KAKU_API_ENDPOINT": "http://localhost:8787",
    "KAKU_WS_ENDPOINT": "ws://localhost:8787",
    "AI_GATEWAY_OPENAI_URL": "https://gateway.ai.cloudflare.com/v1/dc1aae994a7608ab1e59a843ff0e5a31/app-dev/openai",
    "AI_GATEWAY_ANTHROPIC_URL": "https://gateway.ai.cloudflare.com/v1/dc1aae994a7608ab1e59a843ff0e5a31/app-dev/anthropic",
    "AI_GATEWAY_GEMINI_URL": "https://gateway.ai.cloudflare.com/v1/dc1aae994a7608ab1e59a843ff0e5a31/app-dev/google-ai-studio",
    "SKIP_CACHE": false,
    "ICE_SERVERS": [
      {
        "urls": "stun:stun.cloudflare.com:3478",
      },
    ],
  },
  "r2_buckets": [
    {
      "binding": "SCREENSHOTS_INBOUND_BUCKET",
      "bucket_name": "app-dev-kz-screenshots-inbound",
    },
  ],
  "env": {
    "dev": {
      "vars": {
        "ENVIRONMENT": "dev",
        "SUNNY_API_ENDPOINT": "https://dev-api.kazeel.com",
        "KAKU_API_ENDPOINT": "https://dev-connections.kazeel.com",
        "KAKU_WS_ENDPOINT": "wss://dev-connections.kazeel.com",
        "AI_GATEWAY_OPENAI_URL": "https://gateway.ai.cloudflare.com/v1/dc1aae994a7608ab1e59a843ff0e5a31/app-dev/openai",
        "AI_GATEWAY_ANTHROPIC_URL": "https://gateway.ai.cloudflare.com/v1/dc1aae994a7608ab1e59a843ff0e5a31/app-dev/anthropic",
        "AI_GATEWAY_GEMINI_URL": "https://gateway.ai.cloudflare.com/v1/dc1aae994a7608ab1e59a843ff0e5a31/app-dev/google-ai-studio",
        "SKIP_CACHE": false,
        "ICE_SERVERS": [
          {
            "urls": "stun:stun.cloudflare.com:3478",
          },
          {
            "urls": "turn:relay1.expressturn.com:3478",
            "username": "ef89RMU4SHUQMSOUU9",
            "credential": "jvkMMnQxWX4Qrhe3",
          },
        ],
      },
      "routes": [
        { "pattern": "dev-connections.kazeel.com", "custom_domain": true },
        {
          "pattern": "dev-app.kazeel.com/cf/v1/connections*",
          "custom_domain": false,
        },
        {
          "pattern": "dev-app.kazeel.com/cf/web/v1/connections*",
          "custom_domain": false,
        },
      ],
      "r2_buckets": [
        {
          "binding": "SCREENSHOTS_INBOUND_BUCKET",
          "bucket_name": "app-dev-kz-screenshots-inbound",
        },
      ],
      "workflows": [
        {
          "name": "connections-workflow",
          "binding": "CONNECTIONS_WORKFLOW",
          "class_name": "ConnectionsWorkflow",
        },
      ],
      "durable_objects": {
        "bindings": [
          {
            "name": "Connections",
            "class_name": "Connections",
          },
          {
            "name": "CoordinatorDO",
            "class_name": "CoordinatorDO",
          },
        ],
      },
    },
    "prod": {
      "vars": {
        "ENVIRONMENT": "prod",
        "SUNNY_API_ENDPOINT": "https://api.kazeel.com",
        "KAKU_API_ENDPOINT": "https://connections.kazeel.com",
        "KAKU_WS_ENDPOINT": "wss://connections.kazeel.com",
        "AI_GATEWAY_OPENAI_URL": "undefined",
        "AI_GATEWAY_ANTHROPIC_URL": "undefined",
        "AI_GATEWAY_GEMINI_URL": "undefined",
        "SKIP_CACHE": false,
        "ICE_SERVERS": [],
      },
      "routes": [
        { "pattern": "connections.kazeel.com", "custom_domain": true },
        {
          "pattern": "app.kazeel.com/cf/v1/connections*",
          "custom_domain": false,
        },
        {
          "pattern": "app.kazeel.com/cf/web/v1/connections*",
          "custom_domain": false,
        },
      ],
      "r2_buckets": [
        {
          "binding": "SCREENSHOTS_INBOUND_BUCKET",
          "bucket_name": "app-primary-kz-screenshots-inbound",
        },
      ],
      "workflows": [
        {
          "name": "connections-workflow",
          "binding": "CONNECTIONS_WORKFLOW",
          "class_name": "ConnectionsWorkflow",
        },
      ],
      "durable_objects": {
        "bindings": [
          {
            "name": "Connections",
            "class_name": "Connections",
          },
          {
            "name": "CoordinatorDO",
            "class_name": "CoordinatorDO",
          },
        ],
      },
    },
  },
  "workflows": [
    {
      "name": "connections-workflow",
      "binding": "CONNECTIONS_WORKFLOW",
      "class_name": "ConnectionsWorkflow",
    },
  ],
  "durable_objects": {
    "bindings": [
      {
        "name": "Connections",
        "class_name": "Connections",
      },
      {
        "name": "CoordinatorDO",
        "class_name": "CoordinatorDO",
      },
    ],
  },
  "migrations": [
    {
      "tag": "v1",
      "new_sqlite_classes": ["Connections"],
    },
    {
      "tag": "v2",
      "new_sqlite_classes": ["CoordinatorDO"],
    },
  ],
  "assets": {
    "directory": "./public/",
    "binding": "ASSETS",
  },
}
