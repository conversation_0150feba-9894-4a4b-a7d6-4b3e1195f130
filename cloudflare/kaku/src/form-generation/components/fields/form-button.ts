/**
 * Form button component
 */

import type { FormButton } from '../../types';
import { getButtonClass, getButtonHTMXAttributes } from '../../utils';

/**
 * Generate form button markup
 */
export function generateFormButton(button: FormButton): string {
  const buttonClass = getButtonClass(button.variant);
  const htmxAttributes = getButtonHTMXAttributes(button);
  const buttonType = button.type === 'submit' ? 'submit' : 'button';

  return `
    <div class="button-container">
      <button
        type="${buttonType}"
        class="${buttonClass}"
        ${htmxAttributes}
      >
        ${button.label}
      </button>
    </div>
  `;
}
