import { html } from 'hono/html';
import { ServiceContext } from './types';

/**
 * Live view component
 */
export const LiveView = (props: ServiceContext) => html`
  <div class="rounded-lg overflow-hidden border border-gray-200 shadow-sm">
    <div
      class="bg-gray-50 px-4 py-2 border-b text-sm text-gray-600 flex justify-between items-center"
    >
      <span>You're currently viewing a ${props.viewType || 'live'} preview</span>
      <button
        class="text-xs text-indigo-600 hover:text-indigo-800"
        hx-get="/api/connections/${props.serviceId}/hide-live-view"
        hx-target="#live-view-container"
        hx-swap="innerHTML"
      >
        Hide Live View
      </button>
    </div>

    <div class="relative">
      <!-- Browser screenshot image from browserbase -->
      <img
        src="${props.browserScreenshot || ''}"
        alt="Live view of ${props.serviceName} login"
        class="w-full"
      />
    </div>
  </div>
`;
