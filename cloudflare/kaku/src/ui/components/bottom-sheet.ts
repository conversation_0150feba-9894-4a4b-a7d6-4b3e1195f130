import { html } from 'hono/html';

/**
 * Renders the "Having Trouble?" bottom sheet content
 */
export const renderHelpBottomSheet = () => {
  return html`
    <div class="p-6 pt-0 text-left">
      <div class="font-semibold text-xl mb-2">Having Trouble?</div>
      <div class="text-[15px] text-[#49454E] mb-6">If you're stuck, choose an option below:</div>
      <div class="flex flex-col">
        <button id="sheet-restart" class="flex items-center justify-between px-2 py-4 bg-transparent border-none w-full text-base text-[#1C1B1F] cursor-pointer">
          <span>Restart</span>
          <span class="text-xl text-[#7A757F]">→</span>
        </button>
        <div class="h-[1px] bg-[#ECE7EB] mx-6"></div>
        <button id="sheet-support" class="flex items-center justify-between px-2 py-4 bg-transparent border-none w-full text-base text-[#1C1B1F] cursor-pointer">
          <span>Contact Support</span>
          <span class="text-xl text-[#7A757F]">→</span>
        </button>
      </div>
      <div class="pt-6">
        <button id="sheet-back" class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary-700 w-full dark:bg-primary-700 dark:hover:bg-primary-600">Back</button>
      </div>
    </div>
  `;
}; 