import { html, raw } from 'hono/html';
import { ServiceContext } from './types';
import { Loading } from './loading';

/**
 * LLM Form Container component
 */
export const LLMFormContainer = (props: ServiceContext & { formContent?: string }) => html`
  <div>
    <h2 class="text-xl font-bold mb-4">${props.formTitle || 'Sign In'}</h2>
    <p class="mb-6 text-gray-600">Type your credentials below to proceed:</p>

    <form
      hx-post="TODO"
      hx-trigger="submit"
      hx-target="#connection-flow"
      hx-swap="innerHTML"
      hx-indicator=".loading-indicator"
    >
      <!-- LLM-generated form fields -->
      ${raw(props.formContent || '')}

      <!-- Loading indicator -->
      <div class="loading-indicator htmx-indicator mt-4">
        ${Loading({ loadingText: 'Submitting...' })}
      </div>
    </form>

    <!-- Live View Toggle -->
    <div class="mt-6 text-center">
      <button
        class="text-sm text-indigo-600 hover:text-indigo-800"
        hx-get="/api/connections/${props.serviceId}/live-view"
        hx-target="#live-view-container"
        hx-swap="innerHTML"
        hx-trigger="click"
      >
        ${props.liveViewToggleText || 'Show Live View'}
      </button>

      <div id="live-view-container" class="mt-4"></div>
    </div>
  </div>
`;
