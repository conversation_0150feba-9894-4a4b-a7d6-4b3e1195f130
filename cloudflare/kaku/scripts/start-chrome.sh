#!/bin/bash

# <PERSON>ript to start Chrome with remote debugging enabled for local development
# This script works on both macOS and Linux

PORT=${1:-9222}

echo "Starting Chrome with remote debugging on port $PORT..."

# Function to check if Chrome is already running with debugging
check_chrome_debug() {
    if curl -s "http://localhost:$PORT/json/version" > /dev/null 2>&1; then
        echo "✓ Chrome is already running with remote debugging on port $PORT"
        echo "You can now run your application!"
        return 0
    else
        return 1
    fi
}

# Check if Chrome is already running with debugging
if check_chrome_debug; then
    exit 0
fi

# Chrome arguments for debugging
CHROME_ARGS=(
    "--remote-debugging-port=$PORT"
    "--remote-allow-origins=*"
    "--auto-accept-this-tab-capture"
    "--disable-web-security"
    "--disable-features=VizDisplayCompositor"
    "--user-data-dir=/tmp/chrome-debug-$PORT"
)

# Detect OS and set Chrome path
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    CHROME_PATH="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
    if [[ ! -f "$CHROME_PATH" ]]; then
        echo "❌ Chrome not found at $CHROME_PATH"
        echo "Please install Google Chrome or update the path in this script"
        exit 1
    fi
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    CHROME_PATH=$(which google-chrome || which chromium-browser || which chrome)
    if [[ -z "$CHROME_PATH" ]]; then
        echo "❌ Chrome not found. Please install google-chrome or chromium-browser"
        exit 1
    fi
else
    echo "❌ Unsupported OS: $OSTYPE"
    echo "Please manually start Chrome with: chrome --remote-debugging-port=$PORT --remote-allow-origins=* --auto-accept-this-tab-capture"
    exit 1
fi

echo "Starting Chrome with debugging enabled..."
echo "Chrome path: $CHROME_PATH"
echo "Debug port: $PORT"

# Start Chrome in the background
"$CHROME_PATH" "${CHROME_ARGS[@]}" > /dev/null 2>&1 &
CHROME_PID=$!

echo "Chrome started with PID: $CHROME_PID"

# Wait a moment for Chrome to start
sleep 3

# Verify Chrome is running with debugging
if check_chrome_debug; then
    echo ""
    echo "🎉 Chrome is now ready for development!"
    echo ""
    echo "To stop Chrome later, run:"
    echo "  kill $CHROME_PID"
    echo ""
    echo "Or find and kill the process:"
    echo "  ps aux | grep 'remote-debugging-port=$PORT'"
    echo ""
else
    echo "❌ Failed to start Chrome with debugging. Please check for errors and try again."
    exit 1
fi
