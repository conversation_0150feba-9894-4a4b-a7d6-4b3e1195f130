# Kazeel Connections API Documentation

## Overview

The Kazeel Connections API provides endpoints for managing platform connections, creating links, and handling authentication flows. This API is built on Cloudflare Workers with Durable Objects for state management.

## Base URLs

### API Routes

- **Production**: `https://api.kazeel.com/v1`
- **Development**: `https://dev-api.kazeel.com/v1`
- **Local**: `http://localhost:8787/v1`

### App Routes (Connection Pages)

- **Production**: `https://connections.kazeel.com`
- **Development**: `https://dev-connections.kazeel.com`
- **Local**: `http://localhost:8787`

## Authentication

All API endpoints require authentication using one of the following methods:

### Method 1: X-Auth-Token Header

```
X-Auth-Token: <auth-token>
```

### Method 2: Cookie (Web Applications)

For web applications, authentication can be provided via cookies.

## Supported Platforms

The API supports the following platforms:

- `facebook` - Facebook
- `netflix` - Netflix
- `github` - GitHub
- `google` - Google
- `kazeel` - <PERSON>zeel
- `test` - Test Platform
- `login_test` - Login Test
- `text_captcha` - Text Captcha Test

---

## API Endpoints

### 1. Create a Link

Creates a new connection link for a specific platform.

**Endpoint:** `POST /connections/:platformId/links`

**Authentication:** Required

**Headers:**

```
Content-Type: application/json
X-Auth-Token: <auth-token>
```

**Path Parameters:**

- `platformId` (string, required) - The platform identifier (e.g., `facebook`, `github`, `google`)

**Example Request:**

```bash
curl -X POST https://api.kazeel.com/v1/connections/facebook/links \
  -H "Content-Type: application/json" \
  -H "X-Auth-Token: your-auth-token-here"
```

**Response (200 OK):**

```json
{
  "linkId": "l_12345678-1234-1234-1234-123456789abc",
  "url": "https://connections.kazeel.com/facebook/l_12345678-1234-1234-1234-123456789abc?userId=user123",
  "expiresAt": 1704067200000
}
```

**Error Responses:**

- `400 Bad Request` - Invalid platformId
- `401 Unauthorized` - Authentication required
- `500 Internal Server Error` - Server error

---

### 2. Get All User's Connections

Retrieves all platform connections for the authenticated user.

**Endpoint:** `GET /connections`

**Authentication:** Required

**Headers:**

```
X-Auth-Token: <auth-token>
```

**Example Request:**

```bash
curl -X GET https://api.kazeel.com/v1/connections \
  -H "X-Auth-Token: your-auth-token-here"
```

**Response (200 OK):**

```json
{
  "connections": [
    {
      "id": "facebook",
      "name": "Facebook",
      "connected": true,
      "logo": "/fb.png"
    },
    {
      "id": "github",
      "name": "Github",
      "connected": false,
      "logo": "/github.png"
    }
  ]
}
```

**Error Responses:**

- `401 Unauthorized` - Authentication required
- `500 Internal Server Error` - Server error

---

### 3. Get Platform Connection

Retrieves connection details for a specific platform.

**Endpoint:** `GET /connections/:platformId`

**Authentication:** Required

**Headers:**

```
X-Auth-Token: <auth-token>
```

**Path Parameters:**

- `platformId` (string, required) - The platform identifier

**Example Request:**

```bash
curl -X GET https://api.kazeel.com/v1/connections/facebook \
  -H "X-Auth-Token: your-auth-token-here"
```

**Response (200 OK):**

```json
{
  "id": "facebook",
  "name": "Facebook",
  "connected": true,
  "logo": "/fb.png",
  "links": [
    {
      "linkId": "l_12345678-1234-1234-1234-123456789abc",
      "status": "active",
      "url": "https://connections.kazeel.com/facebook/l_12345678-1234-1234-1234-123456789abc?userId=user123",
      "createdAt": 1703980800000,
      "expiresAt": 1704067200000
    }
  ],
  "sessionData": {
    "cookies": [],
    "localStorageData": {},
    "sessionStorageData": {},
    "lastUpdated": 1703980800000
  },
  "retryCount": 0
}
```

**Error Responses:**

- `401 Unauthorized` - Authentication required
- `500 Internal Server Error` - Server error

---

### 4. Disconnect from Platform

Disconnects from a specific platform and clears session data.

**Endpoint:** `DELETE /connections/:platformId`

**Authentication:** Required

**Headers:**

```
X-Auth-Token: <auth-token>
```

**Path Parameters:**

- `platformId` (string, required) - The platform identifier

**Example Request:**

```bash
curl -X DELETE https://api.kazeel.com/v1/connections/facebook \
  -H "X-Auth-Token: your-auth-token-here"
```

**Response (200 OK):**

```json
{
  "success": true,
  "message": "Successfully disconnected from facebook"
}
```

**Error Responses:**

- `401 Unauthorized` - Authentication required
- `404 Not Found` - Platform not found or not connected
- `500 Internal Server Error` - Server error

---

### 5. Delete All Connections

Deletes all connections for the authenticated user.

**Endpoint:** `DELETE /connections`

**Authentication:** Required

**Headers:**

```
X-Auth-Token: <auth-token>
```

**Example Request:**

```bash
curl -X DELETE https://api.kazeel.com/v1/connections \
  -H "X-Auth-Token: your-auth-token-here"
```

**Response (204 No Content):**

```
(Empty response body)
```

**Error Responses:**

- `401 Unauthorized` - Authentication required
- `500 Internal Server Error` - Server error

---

## App-Specific Endpoints

### 6. Reset Link

Resets a connection link by creating a new one and invalidating the old one.

**Endpoint:** `GET /app/:linkId/reset`

**Authentication:** Not required (uses query parameters)

**Query Parameters:**

- `userId` (string, required) - The user identifier
- `platformId` (string, required) - The platform identifier

**Example Request:**

```bash
curl -X GET "https://connections.kazeel.com/app/l_12345678-1234-1234-1234-123456789abc/reset?userId=user123&platformId=facebook"
```

**Response (302 Redirect):**

```
Location: https://connections.kazeel.com/facebook/l_new-link-id?userId=user123
```

**Error Responses:**

- `400 Bad Request` - Missing userId or platformId query parameters
- `404 Not Found` - Link not found or not active
- `429 Too Many Requests` - Retry limit exceeded

---

### 7. Main Connection Page

Serves the main connection page for a platform and link.

**Endpoint:** `GET /:platformId/:linkId`

**Authentication:** Not required (uses query parameters)

**Path Parameters:**

- `platformId` (string, required) - The platform identifier
- `linkId` (string, required) - The link identifier

**Query Parameters:**

- `userId` (string, required) - The user identifier

**Example Request:**

```bash
curl -X GET "https://connections.kazeel.com/facebook/l_12345678-1234-1234-1234-123456789abc?userId=user123"
```

**Response (200 OK):**

```html
<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Connect to Facebook - Kazeel</title>
    <!-- HTML content for the connection page -->
  </head>
  <body>
    <!-- Connection interface -->
  </body>
</html>
```

**Error Responses:**

- `400 Bad Request` - Missing linkId or platformId
- `500 Internal Server Error` - Link not found or not active

---

### 8. Connection Flow Page

Serves the connection flow page with WebSocket support.

**Endpoint:** `GET /:platformId/:linkId/flow`

**Authentication:** Not required (uses query parameters)

**Path Parameters:**

- `platformId` (string, required) - The platform identifier
- `linkId` (string, required) - The link identifier

**Query Parameters:**

- `userId` (string, required) - The user identifier

**Example Request:**

```bash
curl -X GET "https://connections.kazeel.com/facebook/l_12345678-1234-1234-1234-123456789abc/flow?userId=user123"
```

**Response (200 OK):**

```html
<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Connection Flow - Kazeel</title>
    <!-- HTML content with WebSocket connection -->
  </head>
  <body>
    <!-- Connection flow interface -->
  </body>
</html>
```

**Error Responses:**

- `400 Bad Request` - Missing linkId or platformId
- `500 Internal Server Error` - Link not found or not active

---

## Internal Endpoints

### 9. Get Links (Internal)

Retrieves all links for a specific user (internal use only).

**Endpoint:** `GET /internal/links`

**Authentication:** Not required

**Query Parameters:**

- `userId` (string, required) - The user identifier

**Example Request:**

```bash
curl -X GET "https://api.kazeel.com/v1/internal/links?userId=user123"
```

**Response (200 OK):**

```json
{
  "platforms": [
    {
      "id": "facebook",
      "links": [
        {
          "linkId": "l_12345678-1234-1234-1234-123456789abc",
          "status": "active",
          "url": "https://connections.kazeel.com/facebook/l_12345678-1234-1234-1234-123456789abc?userId=user123",
          "createdAt": 1703980800000,
          "expiresAt": 1704067200000
        }
      ],
      "sessionData": {
        "cookies": [],
        "localStorageData": {},
        "sessionStorageData": {},
        "lastUpdated": 1703980800000
      },
      "connected": true,
      "retryCount": 0
    }
  ]
}
```

**Error Responses:**

- `400 Bad Request` - Missing userId

---

### 10. Delete All Connections (Internal)

Deletes all connections for a specific user (internal use only).

**Endpoint:** `DELETE /internal/connections`

**Authentication:** Not required

**Headers:**

```
X-Impersonated-UserId: <user-id>
```

**Example Request:**

```bash
curl -X DELETE https://api.kazeel.com/v1/internal/connections \
  -H "X-Impersonated-UserId: user123"
```

**Response (200 OK):**

```json
{
  "success": true
}
```

**Error Responses:**

- `400 Bad Request` - Missing X-Impersonated-UserId header

---

## WebSocket Endpoints

### 11. Agent WebSocket Connection

WebSocket endpoint for real-time communication with connection agents.

**Endpoint:** `WS /agents/connections/:linkId`

**Authentication:** Not required

**Path Parameters:**

- `linkId` (string, required) - The link identifier

**Example Connection:**

```javascript
const ws = new WebSocket(
  'wss://api.kazeel.com/v1/agents/connections/l_12345678-1234-1234-1234-123456789abc',
);
```

---

## Data Types

### LinkInfo

```typescript
interface LinkInfo {
  linkId: string; // Unique identifier for the link
  status: 'active' | 'expired' | 'connected' | 'not_available';
  url: string; // URL for the connection
  createdAt: number; // Timestamp when link was created
  expiresAt: number; // Timestamp when link expires (24 hours from creation)
  connectedAt?: number; // Timestamp when link was connected (if applicable)
  agentId?: string; // Current AgentDO identifier for this link
}
```

### PlatformMetadata

```typescript
interface PlatformMetadata {
  id: PlatformTypes; // Platform identifier
  links: LinkInfo[]; // List of links for this platform
  sessionData?: UserSessionData; // Session data for this platform
  connected: boolean; // Whether this platform is connected
  retryCount: number; // Current retry count for this platform
}
```

### UserSessionData

```typescript
interface UserSessionData {
  cookies: Cookie[]; // Browser cookies
  localStorageData: Record<string, string | null>; // Local storage data
  sessionStorageData: Record<string, string | null>; // Session storage data
  lastUpdated?: number; // Timestamp when session data was last updated
}
```

---

## Error Handling

All API endpoints return appropriate HTTP status codes and error messages in JSON format:

```json
{
  "error": "Error message description"
}
```

Common error codes:

- `400 Bad Request` - Invalid request parameters
- `401 Unauthorized` - Authentication required or failed
- `404 Not Found` - Resource not found
- `429 Too Many Requests` - Rate limit exceeded
- `500 Internal Server Error` - Server error

---

## Rate Limits

- **Link Creation**: 3 attempts per platform per user
- **Reset Operations**: 3 attempts per platform per user
- **General API**: 100 requests per minute per user

---

## WebSocket Events

The WebSocket connection supports the following events:

### Client to Server

- `form_submit` - Submit form data
- `screenshot_request` - Request current screenshot
- `state_request` - Request current state

### Server to Client

- `form_update` - Form data update
- `screenshot_update` - Screenshot update
- `state_update` - State update
- `error` - Error notification

---

## Examples

### Complete Flow Example

1. **Create a link:**

```bash
curl -X POST https://api.kazeel.com/v1/connections/facebook/links \
  -H "Content-Type: application/json" \
  -H "X-Auth-Token: your-auth-token-here"
```

2. **Access the connection page:**

```bash
curl -X GET "https://connections.kazeel.com/facebook/l_12345678-1234-1234-1234-123456789abc?userId=user123"
```

3. **Reset the link if needed:**

```bash
curl -X GET "https://connections.kazeel.com/app/l_12345678-1234-1234-1234-123456789abc/reset?userId=user123&platformId=facebook"
```

4. **Disconnect when done:**

```bash
curl -X DELETE https://api.kazeel.com/v1/connections/facebook \
  -H "X-Auth-Token: your-auth-token-here"
```

---
