@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  form {
    @apply w-full;
  }

  /* Form Layout */
  .form-container {
    @apply flex flex-col w-full rounded-lg;
  }

  /* Typography */
  .form-title {
    @apply text-base font-bold text-surface-on-variant py-1;
  }

  .form-description {
    @apply text-base text-background-on-background text-start mb-6 mt-1;
  }

  .form-label {
    @apply block text-sm font-medium text-neutral-60 mb-2;
  }

  .form-error {
    @apply text-sm text-error my-2 py-1;
  }

  .form-help-text {
    @apply text-xs text-neutral-50 mt-1;
  }

  /* Input Fields */
  .input-container {
    @apply inline-flex flex-col space-y-1 w-full mb-4 justify-center items-start;
  }

  /* Floating Label Input */
  .floating-input-wrapper {
    @apply relative w-full;
  }

  .floating-input {
    @apply block pt-4 pb-2.5 px-2.5 w-full text-sm text-surface-on-surface bg-transparent rounded-lg border border-outline appearance-none focus:outline-none focus:ring-0 focus:border-primary focus:border-2 transition-all duration-300;
  }

  .floating-label {
    position: absolute;
    font-size: 0.875rem;
    line-height: 1.25rem;
    color: rgb(148 143 153); /* neutral-variant-60 for better contrast */
    transition: all 0.3s ease;
    transform-origin: left top;
    top: 50%;
    left: 0.625rem;
    transform: translateY(-50%);
    z-index: 10;
    padding: 0 0.25rem;
    pointer-events: none;
  }

  .floating-input:focus + .floating-label,
  .floating-input:not(:placeholder-shown) + .floating-label {
    @apply bg-inverse-on-surface;
    transform: translateY(-0.4rem) scale(0.75);
    color: rgb(101 85 143); /* primary */
    top: 0;
  }

  .floating-input::placeholder {
    @apply text-transparent;
  }

  .input-field {
    @apply w-full px-4 py-2 bg-transparent border border-outline rounded-[4px] text-surface-on-surface focus:outline-none focus:border-primary transition duration-200;
  }

  .input-field-error {
    @apply w-full px-4 py-2 bg-surface-dark border border-error rounded-lg text-surface-dark-on-surface focus:outline-none focus:border-error;
  }

  /* Checkbox & Radio */
  .checkbox-container {
    @apply flex items-center space-x-2 mb-4;
  }

  .checkbox-label {
    @apply text-sm text-neutral-70;
  }

  .checkbox-field {
    @apply h-4 w-4 rounded border-outline text-primary focus:ring-primary bg-surface-dark;
  }

  /* Password Toggle */
  .password-toggle-container {
    @apply flex items-center space-x-2 mt-2;
  }

  .password-toggle-checkbox {
    @apply h-4 w-4 rounded border-outline text-primary focus:ring-primary bg-surface-dark;
  }

  .password-toggle-label {
    @apply text-sm text-neutral-70 cursor-pointer;
  }

  .password-toggle-text {
    @apply select-none;
  }

  /* Modern Radio Buttons */
  .radio-fieldset {
    @apply w-full;
  }

  .radio-legend {
    @apply text-base font-medium text-surface-on-surface mb-3;
  }

  .radio-options {
    @apply space-y-3;
  }

  .radio-group {
    @apply relative;
  }

  .radio-input {
    @apply sr-only;
  }

  .radio-label {
    @apply flex items-center cursor-pointer p-4 rounded-lg border border-outline bg-surface-container hover:bg-surface-container-high transition-all duration-200 ease-in-out;
  }

  .radio-label:hover {
    @apply border-primary-40 shadow-sm;
  }

  .radio-input:checked + .radio-label {
    @apply border-primary bg-primary-container text-primary-on-container;
  }

  .radio-input:focus + .radio-label {
    @apply ring-2 ring-primary ring-opacity-50;
  }

  .radio-button {
    @apply w-5 h-5 rounded-full border-2 border-outline mr-3 flex-shrink-0 relative transition-all duration-200;
  }

  .radio-input:checked + .radio-label .radio-button {
    @apply border-primary bg-primary;
  }

  .radio-input:checked + .radio-label .radio-button::after {
    content: '';
    @apply absolute top-1/2 left-1/2 w-2 h-2 bg-primary-on-primary rounded-full transform -translate-x-1/2 -translate-y-1/2;
  }

  .radio-text {
    @apply text-sm font-medium flex-1;
  }

  .radio-input:checked + .radio-label .radio-text {
    @apply text-primary-on-container;
  }

  /* Select */
  .select-container {
    @apply flex flex-col space-y-1 w-full mb-4;
  }

  .select-field {
    @apply w-full px-4 py-2 bg-surface-dark border border-outline rounded-lg text-surface-dark-on-surface focus:outline-none focus:border-primary appearance-none;
  }

  /* Buttons */
  .button-container {
    @apply flex space-x-3 w-full mt-3;
  }

  .button-primary {
    @apply w-full py-2 px-4 bg-primary hover:bg-primary-40 text-primary-on-primary rounded-lg transition duration-200;
  }

  .button-secondary {
    @apply w-full py-2 px-4 border border-secondary bg-secondary hover:bg-secondary-40 text-secondary-on-secondary font-semibold rounded-lg transition duration-200;
  }

  .button-link {
    @apply w-full py-2 px-4 border border-primary/30 bg-primary/10 hover:bg-primary/20 hover:border-primary/50 text-primary font-medium rounded-lg transition duration-200 shadow-sm hover:shadow-md;
  }

  .button-danger {
    @apply w-full py-2 px-4 bg-error hover:bg-error-dark text-error-on-error font-semibold rounded-lg transition duration-200;
  }

  /* OTP Input */
  .otp-container {
    @apply flex justify-between space-x-2 w-full mb-4;
  }

  .otp-input {
    @apply w-12 h-12 text-center bg-surface-dark border border-outline rounded-lg text-surface-dark-on-surface text-xl focus:outline-none focus:border-primary;
  }

  /* 2FA and Security Elements */
  .security-badge {
    @apply inline-flex items-center space-x-1 px-2 py-1 bg-surface-dark rounded text-xs text-neutral-70 mb-4;
  }

  .security-icon {
    @apply w-4 h-4 text-primary;
  }

  /* Loading States */
  .loading-spinner {
    @apply animate-spin h-5 w-5 text-primary-on-primary;
  }

  .button-loading {
    @apply opacity-75 cursor-not-allowed;
  }

  /* Shimmer Animation */
  @keyframes shimmer {
    0% {
      background-position: -200px 0;
    }
    100% {
      background-position: calc(200px + 100%) 0;
    }
  }

  .shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
  }

  .dark .shimmer {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size: 200px 100%;
  }

  /* Captcha Container */
  .captcha-container {
    @apply w-full bg-surface-dark border border-outline rounded-lg p-4 mb-4 flex justify-center items-center;
  }

  /* Form Sections */
  .form-section {
    @apply border-t border-outline pt-4 mt-4;
  }

  .form-section-title {
    @apply text-lg font-medium text-surface-on-surface mb-4;
  }

  /* Form Footer */
  .form-footer {
    @apply text-xs text-neutral-50 text-center mt-6;
  }

  /* Link Styles */
  .form-link {
    @apply text-primary hover:text-primary-40 underline;
  }

  /* Error Display */
  .form-error {
    @apply relative bg-error-container/20 border border-error/30 rounded-xl p-4 mb-4 transition-all duration-200 text-sm leading-relaxed text-error font-medium pr-8;
  }

  .form-error:hover {
    @apply bg-error-container/30 border-error/40;
  }

  .form-error::before {
    content: '⚠️';
    @apply absolute top-3 right-3 text-lg opacity-70;
  }

  /* Prompt Display */
  .prompt-container {
    @apply flex flex-col mb-4;
  }

  .verification-code-display {
    @apply relative bg-gradient-to-br from-primary-container/20 to-primary-container/30 border border-primary-container/50 rounded-lg p-4 transition-all duration-300 shadow-md hover:shadow-lg;
  }

  .verification-code-display:hover {
    @apply bg-gradient-to-br from-primary-container/30 to-primary-container/40 border-primary-container/70 transform scale-[1.01];
  }

  .verification-code-display::before {
    content: '';
    @apply absolute top-0 left-0 right-0 h-0.5 bg-gradient-to-r from-primary via-primary-60 to-primary-40 rounded-t-lg;
  }

  .verification-code-display::after {
    content: '✨';
    @apply absolute top-2.5 right-3 text-sm opacity-50;
  }

  .verification-code-label {
    @apply text-xs font-semibold text-primary-on-container/70 uppercase tracking-wide mb-2 flex items-center gap-1.5;
  }

  .verification-code-label::before {
    content: '🔐';
    @apply text-xs;
  }

  .verification-code-value {
    @apply font-mono text-lg mb-2 font-bold text-primary tracking-wider select-all cursor-pointer bg-surface/70 border border-primary-container/30 rounded-md px-3 py-4 text-[2rem] text-center transition-all duration-200 shadow-sm hover:shadow-md backdrop-blur-sm;
  }

  .contextual-info {
    @apply relative bg-primary-container/10 border border-primary-container/30 rounded-xl p-4 pl-8 mb-4 transition-all duration-200;
  }

  .contextual-info:hover {
    @apply bg-primary-container/15 border-primary-container/40;
  }

  .contextual-info-text {
    @apply text-sm leading-relaxed text-primary-40 font-medium relative;
  }

  .contextual-info-text::before {
    content: '💡';
    @apply absolute -left-6 top-0 text-base opacity-70;
  }
}
