import { describe, it, expect } from 'vitest';
import { passwordEncoder } from '../../src/common/security/passwordEncoder';

describe('Password encoder', () => {
  it('should encode and decode password', async () => {
    const password = 'test-password';
    const encodedPassword = await passwordEncoder().encode(password);
    const matches = await passwordEncoder().matches(password, encodedPassword);
    expect(matches).toBe(true);
  });
  it('should decode a password encoded by <PERSON>', async () => {
    const password = 'this is an awesome password';
    const encodedPassword =
      '$argon2id$v=19$m=16384,t=2,p=1$J1lDBqWTpije3hfNicl6nA$Ktm+S5MLIWMeGMJ3v7QWCCW6U83Ub4MUisYPwlG8Zcs';
    const matches = await passwordEncoder().matches(password, encodedPassword);
    expect(matches).toBe(true);
  });
  it('should decode a password encoded by <PERSON> with spring prefix', async () => {
    const password = 'this is an awesome password';
    const encodedPasswordWithPrefix =
      '{argon2@SpringSecurity_v5_8}$argon2id$v=19$m=16384,t=2,p=1$J1lDBqWTpije3hfNicl6nA$Ktm+S5MLIWMeGMJ3v7QWCCW6U83Ub4MUisYPwlG8Zcs';
    const matchesWithPrefix = await passwordEncoder().matches(password, encodedPasswordWithPrefix);
    expect(matchesWithPrefix).toBe(true);
  });
  it('should fail to match password', async () => {
    const wrongPassword = 'test-password123';
    const encodedPassword =
      '$argon2id$v=19$m=16384,t=2,p=1$J1lDBqWTpije3hfNicl6nA$Ktm+S5MLIWMeGMJ3v7QWCCW6U83Ub4MUisYPwlG8Zcs';
    const matches = await passwordEncoder().matches(wrongPassword, encodedPassword);
    expect(matches).toBe(false);
  });
});
