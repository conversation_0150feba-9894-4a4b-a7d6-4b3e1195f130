/**
 * Chrome DevTools Protocol (CDP) Event Types
 * 
 * These interfaces define the structure of CDP events received from the browser.
 */

export interface CDPAttachedToTargetParams {
  sessionId: string;
  targetInfo: {
    type: string;
    [key: string]: unknown;
  };
}

export interface CDPRuntimeBindingCalledParams {
  name: string;
  payload: string;
  executionContextId: number;
}

export interface CDPRuntimeExceptionParams {
  exceptionDetails: {
    text: string;
    [key: string]: unknown;
  };
}

export interface CDPConsoleAPIParams {
  type: string;
  exceptionDetails?: {
    text?: string;
    [key: string]: unknown;
  };
  [key: string]: unknown;
}

export interface CDPEvent<T = unknown> {
  params: T;
}
