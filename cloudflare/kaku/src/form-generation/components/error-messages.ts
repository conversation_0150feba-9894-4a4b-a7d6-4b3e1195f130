/**
 * Error messages component
 */

import type { FormMetadata } from '../types';

/**
 * Extract error messages from form metadata
 */
export function getErrorMessages(metadata: FormMetadata): string[] {
  const errors: string[] = [];

  if (metadata.errors?.length) {
    errors.push(...metadata.errors);
  }

  return errors;
}

/**
 * Generate error messages markup
 */
export function generateErrorMessages(errors: string[]): string {
  const errorElements = errors.map((error) => `<div class="form-error">${error}</div>`);
  return errorElements.join('\n');
}
