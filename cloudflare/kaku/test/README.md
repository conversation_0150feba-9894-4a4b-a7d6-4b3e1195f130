# Test Structure

This project has been organized to separate tests that run in different environments:

## Unit Tests (`test/unit/`)

These tests run in the Node.js environment using the `--pool=threads` flag and are designed for testing pure JavaScript/TypeScript utilities and functions that don't require Cloudflare Workers runtime.

**Location**: `test/unit/`
**Command**: `npm run test:node`
**Environment**: Node.js with threads pool
**Configuration**: `vitest.unit.config.ts`

**Current tests**:

- `test/unit/client/screenshot-comparison.test.mjs` - Tests screenshot comparison utilities
- `test/unit/client/captcha-detector-debug.test.mjs` - Tests captcha detector debug utilities

## Integration Tests (`test/integration/`)

These tests require the Cloudflare Workers environment and test functionality that depends on Workers bindings like R2 buckets, Durable Objects, and Workflows.

**Location**: `test/integration/`
**Command**: `npm run test:workers`
**Environment**: Cloudflare Workers runtime via vitest-pool-workers
**Configuration**: `vitest.integration.config.ts`

**Current tests**:

- `test/integration/passwordEncoder.test.ts` - ✅ **PASSING** (4/4 tests) - Tests password encoding with WASM support
- `test/integration/connections-agent.test.ts` - ❌ **FAILING** - Tests the Hono API with Workers bindings (needs Durable Object setup)
- `test/integration/workflow-test.test.ts` - ❌ **FAILING** - Tests R2 bucket functionality (needs R2 binding setup)

**Known Issues**:

- Durable Object bindings are not properly configured in the test environment
- R2 bucket bindings are not available in the test environment
- The vitest-pool-workers configuration needs additional setup for complex Workers bindings

## Running Tests

### All Unit Tests (Default)

```bash
npm test
# or
npm run test:node
```

### Integration Tests (Workers Environment)

```bash
npm run test:workers
```

### Watch Mode

```bash
# Unit tests
npm run test:watch:node

# Integration tests
npm run test:watch:workers
```

## Notes

- Unit tests are fast and reliable, running in Node.js environment
- Integration tests require proper Cloudflare Workers runtime setup
- The screenshot comparison test demonstrates excellent performance with pixel sampling
- Integration tests may require additional configuration for complex Workers bindings

## Current Status

✅ **Unit Tests**: All working perfectly (19/19 tests passing)

- Screenshot comparison utilities with performance benchmarks
- Captcha detector debug utilities
- Fast execution in Node.js environment

⚠️ **Integration Tests**: Partially working (4/13 tests passing)

- Password encoder tests work in Workers environment
- API and R2 tests need additional binding configuration
- Workers runtime starts successfully but bindings are not properly connected

## Fixing Integration Tests

To fix the remaining integration test issues:

1. **Durable Object Binding**: The `Connections` class needs proper export and binding setup
2. **R2 Bucket Binding**: The `SCREENSHOTS_INBOUND_BUCKET` needs proper configuration
3. **Environment Variables**: Additional environment variables may be needed

## Test Results

The screenshot comparison test shows excellent performance:

- Supports different image sizes (100x100 to 1920x1080)
- Pixel sampling provides significant performance improvements (up to 8x faster)
- Accurate difference detection with configurable thresholds
- Comprehensive edge case handling
