import { env, waitOnExecutionContext, createExecutionContext } from 'cloudflare:test';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { fetchMock } from 'cloudflare:test';
import app, { Connections } from '../../src/api';
import {
  ConnectionWorkflowState,
  storeConnectionScreenshotToR2,
} from '../../src/common/utils/storeConnectionScreenshotToR2';
import { CreateLinkResponse } from '../../src/shared/coordinator-types';

// Helper function to create user and setup authentication mock
const createUser = (username: string, password: string, userId: string) => {
  const basicToken = btoa(`${username}:${password}`);
  setupUserFetch(basicToken, { userId });
  return basicToken;
};

const setupUserFetch = (requestedUserAuthKey: string, response: any, status = 200) => {
  const pingUserPath = `v1/users`;
  return fetchMock
    .get(env.SUNNY_API_ENDPOINT)
    .intercept({
      path: pingUserPath,
      headers: {
        'X-Auth-Token': `Basic ${requestedUserAuthKey}`,
      },
      method: 'get',
    })
    .reply(status, response)
    .persist();
};

describe.skip('should render the initial htmx correctly', () => {
  beforeEach(async () => {
    fetchMock.activate();
  });

  afterEach(async () => {
    fetchMock.deactivate();
  });

  it('should render service page with provided linkId and serviceId', async () => {
    const userId = 'testUser';
    const platformId = 'facebook';
    const username = 'testuser';
    const password = 'testpass';
    const basicToken = createUser(username, password, userId);

    const createLinkRequest = new Request(
      `http://localhost:8787/cf/v1/connections/${platformId}/links`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Auth-Token': `Basic ${basicToken}`,
        },
      },
    );
    const ctx = createExecutionContext();
    const createLinkResponse = await app.fetch(createLinkRequest, env, ctx);
    await waitOnExecutionContext(ctx);
    expect(createLinkResponse.status).toBe(200);
    const linkResponse = (await createLinkResponse.json()) as CreateLinkResponse;
    expect(linkResponse).toHaveProperty('linkId');

    // mock stub.eagerlyInitializeResources() to do nothing
    // else it will try to open up a browser and it fails.
    vi.spyOn(Connections.prototype, 'eagerlyInitializeResources').mockImplementation(async () => {
      return;
    });

    const request = new Request(
      `http://localhost:8787/${platformId}/${linkResponse.linkId}?userId=${userId}`,
      { method: 'GET' },
    );
    // @ts-ignore no execution Context on the request context
    const response = await app.fetch(request, env, { waitUntil: vi.fn() as any });

    expect(response.status).toBe(200);
    const text = await response.text();

    // Check that the response contains the expected rendered HTML content
    expect(text).toContain('Facebook');
    expect(text).toContain(userId);
  });

  it('it should store screenshots to R2', async () => {
    //Arrange
    const userId = 'u_testuser1234';
    const platformId = 'facebook';
    const sessionId = '1234';
    const screenshotInput = 'this is our fake screenshot';

    const workflowState = ConnectionWorkflowState.Authenticated;

    //Act
    await storeConnectionScreenshotToR2(
      env.SCREENSHOTS_INBOUND_BUCKET,
      userId,
      platformId,
      sessionId,
      workflowState,
      screenshotInput,
    );

    //Assert
    const expectedPath = `base64_screenshots/${userId}/${platformId}/${sessionId}_${workflowState}_`;

    const response = await env.SCREENSHOTS_INBOUND_BUCKET.list({
      startAfter: expectedPath,
    });

    const completeKey = response.objects[0].key;

    const screenshotOutput = await env.SCREENSHOTS_INBOUND_BUCKET.get(completeKey);

    if (screenshotOutput) {
      const blob = await screenshotOutput.blob();
      const arrayBuffer = await blob.arrayBuffer();
      const uint8 = new Uint8Array(arrayBuffer);

      expect(uint8).toStrictEqual(convertBase64ToBytes(screenshotInput));
    } else {
      //Fail the test, response shouldn't be null
      expect(true).toBe(false);
    }
  });
});

function convertBase64ToBytes(input: string): ArrayBuffer {
  const binaryString = atob(input);
  const len = binaryString.length;
  const bytes = new Uint8Array(len);
  for (let i = 0; i < len; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  return bytes as any;
}
