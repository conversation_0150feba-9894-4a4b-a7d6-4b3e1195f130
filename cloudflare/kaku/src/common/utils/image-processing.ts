/**
 * Image Processing Utilities for Browser Automation
 *
 * This module provides utilities for processing images before sending them to LLMs,
 * including grayscaling functionality to reduce token usage and improve consistency.
 */

/**
 * Converts a base64 image to grayscale using canvas operations
 *
 * @param base64Image - Base64 encoded image (with or without data URL prefix)
 * @returns Promise<string> - Base64 encoded grayscale image
 */
export async function convertToGrayscale(base64Image: string): Promise<string> {
  // Clean the base64 string
  const cleanBase64 = base64Image.replace(/^data:image\/[^;]+;base64,/, '');

  return new Promise((resolve, reject) => {
    const img = new Image();

    img.onload = () => {
      try {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        if (!ctx) {
          throw new Error('Failed to get canvas 2D context');
        }

        canvas.width = img.width;
        canvas.height = img.height;

        ctx.drawImage(img, 0, 0);

        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;

        // Convert to grayscale using luminance formula
        // Y = 0.299*R + 0.587*G + 0.114*B
        for (let i = 0; i < data.length; i += 4) {
          const r = data[i];
          const g = data[i + 1];
          const b = data[i + 2];

          // Calculate grayscale value using luminance formula
          const gray = Math.round(0.299 * r + 0.587 * g + 0.114 * b);

          // Set RGB channels to the same grayscale value
          data[i] = gray; // Red
          data[i + 1] = gray; // Green
          data[i + 2] = gray; // Blue
          // Alpha channel (data[i + 3]) remains unchanged
        }

        // Put the modified image data back onto the canvas
        ctx.putImageData(imageData, 0, 0);

        // Convert to base64 with WebP format
        const base64Result = canvas.toDataURL('image/webp', 0.9);

        // Remove the data URL prefix to return just the base64 data
        const cleanResult = base64Result.replace(/^data:image\/[^;]+;base64,/, '');
        resolve(cleanResult);
      } catch (error) {
        reject(new Error(`Failed to convert image to grayscale: ${error}`));
      }
    };

    img.onerror = () => {
      reject(new Error('Failed to load image for grayscale conversion'));
    };

    // Set the image source
    img.src = `data:image/png;base64,${cleanBase64}`;
  });
}
