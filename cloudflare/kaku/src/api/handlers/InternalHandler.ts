import { <PERSON>o } from 'hono';
import { <PERSON><PERSON><PERSON><PERSON> } from '../../common/types';
import { IMPERSONATE_USER_ID, USER_ID, LINK_ID, PLATFORM_ID } from '../constants';
import { platformDetails, PlatformTypes } from '../../ui/constants';

const app = new Hono<KakuApp>();

// Internal-only debugging endpoints
// Note: Keep these behind feature flags or auth as needed in the future
app
  .get('/links', async (c) => {
    const userId = (c.req.query(USER_ID) as string) ?? '';
    if (!userId) {
      return c.json({ error: 'Missing userId' }, 400);
    }

    const coordinator = c.env.CoordinatorDO.idFromName(userId);
    const stub = c.env.CoordinatorDO.get(coordinator);
    const links = await stub.state;
    return c.json(links);
  })
  .delete(
    '/connections',
    // TODO: Add basic Auth
    // basicAuthHeader()
    async (c) => {
      const userId = c.req.header(IMPERSONATE_USER_ID);
      if (!userId) {
        return c.json({ error: 'Missing X-Impersonated-UserId header' }, 400);
      }

      const coordinator = c.env.CoordinatorDO.idFromName(userId);
      const stub = c.env.CoordinatorDO.get(coordinator);
      await stub.deleteAll();
      return c.json({ success: true });
    },
  )
  .get(`/:${PLATFORM_ID}/links`, async (c) => {
    try {
      const platformId = c.req.param(PLATFORM_ID) as PlatformTypes;
      const userId = c.req.header(IMPERSONATE_USER_ID);
      if (!userId) {
        return c.json({ error: 'Missing X-Impersonated-UserId header' }, 400);
      }

      const detail = platformDetails[platformId];
      if (!detail) {
        return c.json({ error: 'Invalid platformId' }, 400);
      }

      const coordinator = c.env.CoordinatorDO.idFromName(userId);
      const stub = c.env.CoordinatorDO.get(coordinator);

      // Get all links for this platform
      const platformData = await stub.getPlatformLinks(platformId);
      return c.json(platformData);
    } catch (error) {
      console.error(
        `[Internal API] Error getting links for platform ${c.req.param(PLATFORM_ID)}:`,
        error,
      );
      return c.json(
        {
          error: error instanceof Error ? error.message : 'Internal Server Error',
        },
        500,
      );
    }
  })
  .post(`/:${PLATFORM_ID}/links/:${LINK_ID}/revoke`, async (c) => {
    try {
      const platformId = c.req.param(PLATFORM_ID) as PlatformTypes;
      const linkId = c.req.param(LINK_ID);
      const userId = c.req.header(IMPERSONATE_USER_ID);
      if (!userId) {
        return c.json({ error: 'Missing X-Impersonated-UserId header' }, 400);
      }

      const detail = platformDetails[platformId];
      if (!detail) {
        return c.json({ error: 'Invalid platformId' }, 400);
      }

      const coordinator = c.env.CoordinatorDO.idFromName(userId);
      const stub = c.env.CoordinatorDO.get(coordinator);

      const revoked = await stub.revokeLink(linkId);

      if (!revoked) {
        return c.json({ error: 'Link not found or already revoked' }, 404);
      }

      return c.json({ success: true, message: 'Link revoked successfully' });
    } catch (error) {
      console.error(`[Internal API] Error revoking link ${c.req.param(LINK_ID)}:`, error);
      return c.json(
        {
          error: error instanceof Error ? error.message : 'Internal Server Error',
        },
        500,
      );
    }
  });

export { app as InternalHandler };
