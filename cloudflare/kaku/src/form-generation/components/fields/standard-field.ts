/**
 * Standard field component
 */

import type { FormField } from '../../types';

/**
 * Generate standard field markup
 */
export function generateStandardField(field: FormField): string {
  return `
    <div class="input-container">
      <label class="form-label" for="${field.id}">${field.label}</label>
      <input
        class="input-field"
        type="${field.fieldControlType}"
        id="${field.id}"
        name="${field.id}"
      >
    </div>
  `;
}
