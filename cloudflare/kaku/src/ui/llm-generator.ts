import { HtmlEscapedString } from 'hono/utils/html';
import { templateEngine, TemplateContext } from './template-engine';
import { html } from 'hono/html';
import { HTMLReturnType } from './types';

/**
 * Service context structure from browserbase
 */
export interface BrowserContext {
  pageTitle: string;
  pageUrl: string;
  serviceName: string;
  serviceId: string;
  forms?: Array<{
    id: string;
    action?: string;
    fields: Array<{
      name: string;
      type: string;
      required?: boolean;
      placeholder?: string;
      label?: string;
    }>;
  }>;
  screenshot?: string;
}

/**
 * Generate HTMX form based on browser context using LLM
 */
export async function generateLoginForm(browserContext: BrowserContext): Promise<string> {
  const prompt = createFormGenerationPrompt(browserContext);

  // In our real implementation, this would call llm
  const llmGeneratedHTML = await mockLLMCall(prompt, browserContext);

  // Create template context
  const templateContext: TemplateContext = {
    serviceName: browserContext.serviceName,
    serviceId: browserContext.serviceId,
    serviceTitle: browserContext.pageTitle,
    browserScreenshot: browserContext.screenshot,
  };

  return templateEngine.renderLLMForm(llmGeneratedHTML, templateContext);
}

function createFormGenerationPrompt(context: BrowserContext): string {
  return `
Create HTMX form fields for a login form for ${context.serviceName}.
The page title is "${context.pageTitle}" and the URL is ${context.pageUrl}.

Guidelines:
1. Use appropriate input types (text, email, password, tel)
2. Create clear, accessible labels
3. Mark required fields appropriately
4. Add validation attributes where appropriate
5. Apply Tailwind CSS styling consistent with a clean, modern interface
6. Include a submit button with proper styling
7. Don't include the surrounding form element, just the fields and button
  `;
}

/**
 * Mock LLM call (placeholder for our actual implementation)
 */
async function mockLLMCall(prompt: string, context: BrowserContext): Promise<string> {
  return html` <h1>TODO</h1> `;
}

export function generateLiveView(browserContext: BrowserContext): HTMLReturnType {
  const templateContext: TemplateContext = {
    serviceName: browserContext.serviceName || 'Unknown Service',
    serviceId: browserContext.serviceId || 'unknown',
    viewType: 'secure',
    browserScreenshot: browserContext.screenshot,
  };

  return templateEngine.renderLiveView(templateContext);
}
