/**
 * Captcha Detector - Advanced screenshot comparison and UI change detection
 *
 * This module provides comprehensive captcha detection functionality through
 * screenshot comparison, UI settling detection, and TensorFlow integration.
 * It monitors visual changes in web pages to detect captcha completion.
 *
 * ## Features
 * - Real-time screenshot comparison with configurable thresholds
 * - UI settling detection for accurate captcha state analysis
 * - TensorFlow integration for object detection
 * - Performance monitoring and debugging capabilities
 * - Frame sampling optimization for better performance
 *
 * ## Type Safety
 * This TypeScript implementation demonstrates proper integration with
 * the screenshotComparisonUtils class, showcasing type safety benefits
 * including compile-time error checking and IntelliSense support.
 */

import type {
  CaptchaDetectorConfig,
  ComparisonResult,
  ImageData as IImageData,
  BoundingBox,
} from '../common/types/common';

// Type definitions for internal use

interface Detection {
  className: string;
  score: number;
  bbox: BoundingBox; // cspell:disable-line
}

interface DetectionResults {
  detections: Detection[];
  highestScoringDetection?: Detection;
}

interface StoredImageData {
  base64: string;
  dimensions: { width: number; height: number };
  timestamp: string;
  frameNumber: number;
}

// Extended comparison result with additional properties for debugging
interface ExtendedComparisonResult extends ComparisonResult {
  totalPixels?: number;
  dimensions?: { width: number; height: number };
  samplingUsed?: number;
}

interface ComparisonHistoryEntry {
  image1: string;
  image2: string;
  result: ExtendedComparisonResult;
  timestamp: string;
  frameNumber: number;
}

interface SettledFrameData {
  data: Uint8ClampedArray;
  dimensions: { width: number; height: number };
}

(function (): void {
  const DEFAULT_CONFIG: CaptchaDetectorConfig = {
    diffThreshold: 5,
    screenshotQuality: 50,
    debug: true,
    comparisonIntervalMs: 150,
    sampling: 2,
    settleThreshold: 1, // Threshold for considering UI settled (percentage)
    consecutiveSettledFrames: 1, // Number of consecutive frames with low difference to consider UI settled
    maxWaitDuration: 2000, // Maximum time to wait for UI to settle (ms) before triggering mouse movement
  };
  const config: CaptchaDetectorConfig = { ...DEFAULT_CONFIG };

  let lastImageData: IImageData | null = null; // For streamed frames method
  let lastImageAlias: string | null = null; // Alias for the last stored image
  let isInitialized: boolean = false;
  let isComparing: boolean = false;
  let isProcessingComparison: boolean = false;
  let frameCount: number = 0; // Counter for frames processed
  let totalConversionTime: number = 0; // Total time spent on frame conversion
  let totalComparisonTime: number = 0; // Total time spent on comparison
  let significantChangeDetected: boolean = false; // Flag to track if a significant change was detected
  let consecutiveSettledFramesCount: number = 0; // Counter for consecutive frames with low difference
  let frameSendingPaused: boolean = false; // Flag to track if frame sending is paused
  let settledFrameData: SettledFrameData | null = null; // Stores the frame data when UI has settled
  let hasSettledFrame: boolean = false; // Flag to track if we have a settled frame

  let mouseMoveTriggerTimeoutId: ReturnType<typeof setTimeout> | null = null; // Timeout ID for mouse movement trigger
  let comparisonTimeoutId: ReturnType<typeof setTimeout> | null = null; // Timeout ID for overall comparison duration

  // Debug storage variables
  const storedImages = new Map<string, StoredImageData>();
  const comparisonHistory: ComparisonHistoryEntry[] = [];
  let imageCounter: number = 0;
  let debugPanel: HTMLElement | null = null;

  /**
   * Initializes the captcha detector with the provided configuration.
   * Sets up the callback to receive frames from the screen cropper.
   *
   * @param options - Configuration options to override defaults
   */
  function initialize(options: Partial<CaptchaDetectorConfig> = {}): void {
    if (isInitialized) return;

    Object.assign(config, options);

    if (config.debug) {
      console.log('Captcha detector initializing with config:', JSON.stringify(config));
    }

    window.addEventListener('beforeunload', handleBeforeUnload);

    // Screenshot comparison utilities will be loaded separately
    if (config.debug) {
      console.log('Screenshot comparison utilities will be loaded separately');
    }

    if (
      window.screenCropper &&
      typeof window.screenCropper.registerCaptchaDetectorCallback === 'function'
    ) {
      window.screenCropper.registerCaptchaDetectorCallback(handleStreamedFrame);
      isInitialized = true;
      console.log('Captcha detector initialized with stream support. Ready for triggers.');
    } else {
      console.error(
        'Captcha detector initialization failed: screenCropper or registerCaptchaDetectorCallback not available.',
      );
    }
  }

  /**
   * Processes frames received from the screen cropper.
   * Compares each frame with the previous one to detect significant changes.
   * Continues comparing frames after a significant change to detect when the UI has settled.
   *
   * @param rgbaBuffer - RGBA buffer containing the frame data
   * @param dimensions - The dimensions of the frame
   */
  async function handleStreamedFrame(
    rgbaBuffer: Uint8Array,
    dimensions: { width: number; height: number },
  ): Promise<void> {
    console.log('Frames received', { isComparing, isProcessingComparison });
    if (!isComparing || isProcessingComparison) {
      return;
    }

    isProcessingComparison = true;
    const tickStartTime = performance.now();
    frameCount++;

    try {
      if (config.debug) {
        console.log(`Processing streamed frame #${frameCount}...`);
      }

      // Start timing the conversion
      performance.mark('conversion-start');

      // Prepare buffer for comparison - more efficient than creating ImageData
      const currentImageData = prepareBufferForComparison(rgbaBuffer, dimensions);

      // Store the current frame for debugging
      const currentImageAlias = storeImageFrame(rgbaBuffer, dimensions);

      // End timing the conversion
      performance.mark('conversion-end');
      performance.measure('Conversion Time', 'conversion-start', 'conversion-end');
      const conversionEntry = performance.getEntriesByName('Conversion Time').pop();
      const conversionTime = conversionEntry?.duration || 0;
      totalConversionTime += conversionTime;

      if (config.debug) {
        console.log(
          `Frame #${frameCount} conversion took ${conversionTime.toFixed(2)} ms (${dimensions?.width || 'unknown'}x${dimensions?.height || 'unknown'})`,
        );
      }

      if (!lastImageData) {
        // If this is the first frame, just store it
        lastImageData = currentImageData;
        lastImageAlias = currentImageAlias; // Store the alias for the last image
        isProcessingComparison = false;
        return;
      }

      // Start timing the comparison
      performance.mark('comparison-start');

      // Compare the images
      let differencePercentage: number;
      let comparisonTime: number;

      // If we have the utility module loaded, use the detailed result
      if (config.debug) {
        console.log('Using screenshot comparison with sampling rate:', config.sampling);
      }

      let comparisonResult: ExtendedComparisonResult | null = null;
      if (window.screenshotComparisonUtils) {
        try {
          const baseResult = window.screenshotComparisonUtils.compareScreenshots(
            lastImageData,
            currentImageData,
            { sampling: config.sampling },
          );

          // Type-safe access to extended properties by casting to our extended type
          comparisonResult = baseResult as ExtendedComparisonResult;
          differencePercentage = comparisonResult.percentageDiff;
          comparisonTime = comparisonResult.comparisonTime;

          if (config.debug && comparisonResult.samplingUsed && comparisonResult.samplingUsed > 1) {
            console.log(
              `Used pixel sampling rate: ${comparisonResult.samplingUsed}x (processed ${Math.round(100 / comparisonResult.samplingUsed)}% of pixels)`,
            );
          }
        } catch (e) {
          console.error(
            'Error using utility compareScreenshots, falling back to simple version:',
            e,
          );
          differencePercentage = compareScreenshots(lastImageData, currentImageData);

          // End timing the comparison
          performance.mark('comparison-end');
          performance.measure('Comparison Time', 'comparison-start', 'comparison-end');
          const comparisonEntry = performance.getEntriesByName('Comparison Time').pop();
          comparisonTime = comparisonEntry?.duration || 0;

          // Create a basic comparison result for storage
          comparisonResult = {
            percentageDiff: differencePercentage,
            comparisonTime: comparisonTime,
            numDiffPixels: 0,
            totalPixels: currentImageData.width * currentImageData.height,
            dimensions: { width: currentImageData.width, height: currentImageData.height },
            samplingUsed: config.sampling || 1,
          };
        }
      } else {
        // Use the simple version that just returns the percentage
        differencePercentage = compareScreenshots(lastImageData, currentImageData);

        // End timing the comparison
        performance.mark('comparison-end');
        performance.measure('Comparison Time', 'comparison-start', 'comparison-end');
        const comparisonEntry = performance.getEntriesByName('Comparison Time').pop();
        comparisonTime = comparisonEntry?.duration || 0;

        // Create a basic comparison result for storage
        comparisonResult = {
          percentageDiff: differencePercentage,
          comparisonTime: comparisonTime,
          numDiffPixels: 0,
          totalPixels: currentImageData.width * currentImageData.height,
          dimensions: { width: currentImageData.width, height: currentImageData.height },
          samplingUsed: config.sampling || 1,
        };
      }

      // Store the comparison result for debugging
      if (lastImageAlias && comparisonResult) {
        storeComparisonResult(lastImageAlias, currentImageAlias, comparisonResult);
      }

      totalComparisonTime += comparisonTime;

      const tickDuration = performance.now() - tickStartTime;

      if (config.debug) {
        console.log(`Frame #${frameCount} comparison took ${comparisonTime.toFixed(2)} ms`);
        console.log(`Total frame #${frameCount} processing took ${tickDuration.toFixed(2)} ms`);
      }

      console.log(`Screenshot comparison: ${differencePercentage.toFixed(2)}% difference`);

      // Check if the difference exceeds the threshold and we haven't detected a significant change yet
      const diffThreshold = config.diffThreshold ?? 5;
      if (differencePercentage > diffThreshold && !significantChangeDetected) {
        console.log(
          `Threshold: ${diffThreshold}%, Difference detected (${differencePercentage.toFixed(2)}%) on frame #${frameCount}`,
        );

        // Log performance statistics
        const avgConversionTime = totalConversionTime / frameCount;
        const avgComparisonTime = totalComparisonTime / frameCount;
        const sampling = config.sampling ?? 1;

        console.log(`
          Performance Statistics:
          - Total frames processed: ${frameCount}
          - Average conversion time: ${avgConversionTime.toFixed(2)} ms
          - Average comparison time: ${avgComparisonTime.toFixed(2)} ms
          - Total conversion time: ${totalConversionTime.toFixed(2)} ms
          - Total comparison time: ${totalComparisonTime.toFixed(2)} ms
          - Pixel sampling rate: ${sampling}x (processed ${Math.round(100 / sampling)}% of pixels)
        `);

        console.log(
          `Pausing frame sending due to significant difference (${differencePercentage.toFixed(2)}%).`,
        );

        // Pause frame sending immediately when significant change is detected
        pauseFrameSending();

        // Mark that we've detected a significant change
        significantChangeDetected = true;
        consecutiveSettledFramesCount = 0;

        // Clear the overall comparison timeout since we detected a change
        clearComparisonTimeoutMonitoring();

        // Start timeout monitoring for UI settling
        startSettleTimeoutMonitoring();

        // We don't stop comparing here, we continue to check if the UI settles
      } else if (significantChangeDetected) {
        // We're in the phase where we're waiting for the UI to settle

        // Check if the UI has settled (difference percentage is below the settle threshold)
        const settleThreshold = config.settleThreshold ?? 1;
        if (differencePercentage <= settleThreshold) {
          consecutiveSettledFramesCount++;

          if (config.debug) {
            const consecutiveFrames = config.consecutiveSettledFrames ?? 1;
            console.log(
              `UI settling: ${differencePercentage.toFixed(2)}% difference, consecutive settled frames: ${consecutiveSettledFramesCount}/${consecutiveFrames}`,
            );
          }

          // If we have enough consecutive frames with low difference, consider the UI settled
          const consecutiveFrames = config.consecutiveSettledFrames ?? 1;
          if (consecutiveSettledFramesCount == consecutiveFrames) {
            console.log(`UI has settled after ${frameCount} frames. Resuming frame sending.`);

            // Clear the settle timeout monitoring since UI has settled
            clearSettleTimeoutMonitoring();

            // Store the settled frame data for TensorFlow detection
            settledFrameData = {
              data: currentImageData.data,
              dimensions: dimensions,
            };
            hasSettledFrame = true;

            // Now that we have a settled frame, run TensorFlow detection
            if (window.tfCaptchaDetector && hasSettledFrame && settledFrameData) {
              console.log('Running TensorFlow detection with settled frame...');
              // Convert Uint8ClampedArray to Uint8Array for TensorFlow detection
              const rgbaBuffer = new Uint8Array(settledFrameData.data);
              const detectionResults = await runTensorFlowDetection(
                rgbaBuffer,
                settledFrameData.dimensions,
              );

              if (
                detectionResults &&
                detectionResults.detections.length > 0 &&
                detectionResults.highestScoringDetection
              ) {
                const highestScoringDetection = detectionResults.highestScoringDetection;
                console.log(
                  `Highest scoring detection: ${highestScoringDetection.className} with score ${highestScoringDetection.score.toFixed(4)}`,
                );

                if (highestScoringDetection.className === 'captcha_resolved') {
                  console.log('Resolved captcha has highest score, falling back to LLM');
                  await notifyCaptchaSolved(differencePercentage, 'tf_detection_resolved');
                } else if (highestScoringDetection.className === 'captcha_unresolved') {
                  console.log(
                    'Unresolved captcha has highest score, continuing monitoring without solved notification',
                  );
                }
              } else {
                console.log('No captcha detected, falling back to LLM');
                await notifyCaptchaSolved(differencePercentage, 'tf_detection_none');
              }
            }

            // Resume frame sending
            if (
              frameSendingPaused &&
              window.screenCropper &&
              typeof window.screenCropper.resumeFrameSending === 'function'
            ) {
              window.screenCropper.resumeFrameSending();
              frameSendingPaused = false;

              if (config.debug) {
                console.log('Resumed frame sending after UI settled');
              }
            }

            // UI has settled and TensorFlow detection is complete - stop the comparison process
            console.log('UI has settled and detection complete. Stopping comparison process.');
            cleanupComparison();
            return; // Exit early since comparison is now complete
          }
        } else {
          // UI is still changing, reset the consecutive settled frames counter
          if (consecutiveSettledFramesCount > 0) {
            if (config.debug) {
              console.log(
                `UI still changing: ${differencePercentage.toFixed(2)}% difference, resetting settled frames counter`,
              );
            }
            consecutiveSettledFramesCount = 0;
          }
        }

        // Update the last image with the current one for future comparisons
        lastImageData = currentImageData;
        lastImageAlias = currentImageAlias;
      } else {
        // Normal case - no significant change detected yet
        // Update the last image with the current one for future comparisons
        lastImageData = currentImageData;
        lastImageAlias = currentImageAlias;

        if (config.debug) {
          console.log(
            `Streaming comparison: ${differencePercentage.toFixed(2)}% difference. Continuing loop.`,
          );
        }
      }

      // Clean up performance marks and measures
      performance.clearMarks('conversion-start');
      performance.clearMarks('conversion-end');
      performance.clearMarks('comparison-start');
      performance.clearMarks('comparison-end');
      performance.clearMeasures('Conversion Time');
      performance.clearMeasures('Comparison Time');
    } catch (error) {
      console.error('Error during streamed frame processing:', error);
    } finally {
      isProcessingComparison = false;

      // Check if we should stop comparing (only if we're not in the middle of waiting for UI to settle)
      if (!isComparing && !significantChangeDetected) {
        // Stop capturing frames for captcha detector
        if (
          window.screenCropper &&
          typeof window.screenCropper.stopCapturingForCaptchaDetector === 'function'
        ) {
          window.screenCropper.stopCapturingForCaptchaDetector();
        }

        if (config.debug) console.log('Comparison stopped.');
      }
    }
  }

  /**
   * Prepares an RGBA buffer for use with pixelmatch.
   *
   * @param rgbaBuffer - RGBA buffer containing the frame data
   * @param dimensions - The dimensions of the frame
   * @returns Object containing the RGBA data and dimensions
   */
  function prepareBufferForComparison(
    rgbaBuffer: Uint8Array,
    dimensions: { width?: number; height?: number },
  ): IImageData {
    const width = dimensions?.width || 800;
    const height = dimensions?.height || 600;
    return {
      data:
        rgbaBuffer instanceof Uint8ClampedArray ? rgbaBuffer : new Uint8ClampedArray(rgbaBuffer),
      width,
      height,
    };
  }

  /**
   * Converts RGBA buffer to base64 data URL for storage and display.
   *
   * @param rgbaBuffer - RGBA buffer containing the frame data
   * @param dimensions - The dimensions of the frame
   * @returns Base64 data URL of the image
   */
  function rgbaToBase64(
    rgbaBuffer: Uint8Array,
    dimensions: { width: number; height: number },
  ): string {
    const canvas = document.createElement('canvas');
    canvas.width = dimensions.width;
    canvas.height = dimensions.height;
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      throw new Error('Failed to get 2D context from canvas');
    }

    const imageData = new ImageData(
      new Uint8ClampedArray(rgbaBuffer),
      dimensions.width,
      dimensions.height,
    );

    ctx.putImageData(imageData, 0, 0);
    return canvas.toDataURL('image/png');
  }

  /**
   * Stores an image frame with an alias for debugging purposes.
   *
   * @param rgbaBuffer - RGBA buffer containing the frame data
   * @param dimensions - The dimensions of the frame
   * @returns The alias assigned to the stored image
   */
  function storeImageFrame(
    rgbaBuffer: Uint8Array,
    dimensions: { width: number; height: number },
  ): string {
    imageCounter++;
    const alias = `image${imageCounter}`;
    const base64 = rgbaToBase64(rgbaBuffer, dimensions);

    storedImages.set(alias, {
      base64,
      dimensions,
      timestamp: new Date().toISOString(),
      frameNumber: frameCount,
    });

    if (config.debug) {
      console.log(`Stored frame as ${alias} (${dimensions.width}x${dimensions.height})`);
    }

    return alias;
  }

  /**
   * Stores comparison result between two image frames.
   *
   * @param image1Alias - Alias of the first image
   * @param image2Alias - Alias of the second image
   * @param comparisonResult - Result from screenshot comparison
   */
  function storeComparisonResult(
    image1Alias: string,
    image2Alias: string,
    comparisonResult: ExtendedComparisonResult,
  ): void {
    const comparison: ComparisonHistoryEntry = {
      image1: image1Alias,
      image2: image2Alias,
      result: comparisonResult,
      timestamp: new Date().toISOString(),
      frameNumber: frameCount,
    };

    comparisonHistory.push(comparison);

    if (config.debug) {
      console.log(
        `Stored comparison: ${image1Alias} vs ${image2Alias} = ${comparisonResult.percentageDiff.toFixed(2)}%`,
      );
    }

    // Update debug panel if it exists
    updateDebugPanel();
  }

  /**
   * Creates a floating debug toggle button.
   */
  function createDebugToggleButton(): void {
    // Check if button already exists
    if (document.getElementById('captcha-debug-toggle')) {
      return;
    }

    const toggleButton = document.createElement('button');
    toggleButton.id = 'captcha-debug-toggle';
    toggleButton.textContent = '🐛';
    toggleButton.title = 'Toggle Screenshot Debug Panel';
    toggleButton.style.cssText = `
      position: fixed;
      bottom: 20px;
      right: 20px;
      width: 50px;
      height: 50px;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      border: 2px solid #333;
      border-radius: 50%;
      cursor: pointer;
      font-size: 20px;
      z-index: 9999;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      transition: all 0.3s ease;
    `;

    // Hover effects
    toggleButton.onmouseenter = () => {
      toggleButton.style.background = 'rgba(0, 0, 0, 0.9)';
      toggleButton.style.transform = 'scale(1.1)';
    };
    toggleButton.onmouseleave = () => {
      toggleButton.style.background = 'rgba(0, 0, 0, 0.8)';
      toggleButton.style.transform = 'scale(1)';
    };

    toggleButton.onclick = () => {
      if (debugPanel) {
        hideDebugPanel();
      } else {
        createDebugPanel();
      }
    };

    document.body.appendChild(toggleButton);
  }

  /**
   * Creates and shows the debug panel for displaying image comparisons.
   */
  function createDebugPanel(): void {
    if (debugPanel) {
      return; // Panel already exists
    }

    debugPanel = document.createElement('div');
    debugPanel.id = 'captcha-debug-panel';
    debugPanel.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      width: 400px;
      max-height: 80vh;
      background: rgba(0, 0, 0, 0.9);
      color: white;
      border: 1px solid #333;
      border-radius: 8px;
      padding: 15px;
      font-family: monospace;
      font-size: 12px;
      z-index: 10000;
      overflow-y: auto;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    `;

    const header = document.createElement('div');
    header.style.cssText = `
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #444;
    `;

    const title = document.createElement('h3');
    title.textContent = 'Screenshot Debug Panel';
    title.style.cssText = 'margin: 0; color: #fff; font-size: 14px;';

    const buttonContainer = document.createElement('div');
    buttonContainer.style.cssText = 'display: flex; gap: 5px; align-items: center;';

    const resetButton = document.createElement('button');
    resetButton.textContent = '🗑️';
    resetButton.title = 'Clear all debug data';
    resetButton.style.cssText = `
      background: #ff8800;
      color: white;
      border: none;
      border-radius: 4px;
      width: 24px;
      height: 24px;
      cursor: pointer;
      font-size: 12px;
      line-height: 1;
    `;
    resetButton.onclick = () => {
      if (confirm('Clear all stored images and comparisons?')) {
        clearDebugDataInternal();
      }
    };

    const closeButton = document.createElement('button');
    closeButton.textContent = '×';
    closeButton.title = 'Close debug panel';
    closeButton.style.cssText = `
      background: #ff4444;
      color: white;
      border: none;
      border-radius: 4px;
      width: 24px;
      height: 24px;
      cursor: pointer;
      font-size: 16px;
      line-height: 1;
    `;
    closeButton.onclick = () => hideDebugPanel();

    buttonContainer.appendChild(resetButton);
    buttonContainer.appendChild(closeButton);

    header.appendChild(title);
    header.appendChild(buttonContainer);

    const content = document.createElement('div');
    content.id = 'debug-panel-content';

    debugPanel.appendChild(header);
    debugPanel.appendChild(content);
    document.body.appendChild(debugPanel);

    updateDebugPanel();
  }

  /**
   * Hides the debug panel without removing it from memory.
   */
  function hideDebugPanel(): void {
    if (debugPanel) {
      debugPanel.style.display = 'none';
    }
  }

  /**
   * Completely removes the debug panel from DOM and memory.
   */
  function removeDebugPanel(): void {
    if (debugPanel) {
      debugPanel.remove();
      debugPanel = null;
    }
  }

  /**
   * Removes the debug toggle button.
   */
  function removeDebugToggleButton(): void {
    const toggleButton = document.getElementById('captcha-debug-toggle');
    if (toggleButton) {
      toggleButton.remove();
    }
  }

  /**
   * Internal function to clear debug data and update the panel.
   */
  function clearDebugDataInternal(): void {
    storedImages.clear();
    comparisonHistory.length = 0;
    imageCounter = 0;
    frameCount = 0;
    updateDebugPanel();

    if (config.debug) {
      console.log('Debug data cleared from panel');
    }
  }

  /**
   * Updates the debug panel content with current comparison data.
   */
  function updateDebugPanel(): void {
    if (!debugPanel) {
      return;
    }

    const content = debugPanel.querySelector('#debug-panel-content');
    if (!content) {
      return;
    }

    let html = `
      <div style="margin-bottom: 15px;">
        <strong>Stored Images: ${storedImages.size}</strong><br>
        <strong>Comparisons: ${comparisonHistory.length}</strong><br>
        <strong>Current Frame: ${frameCount}</strong>
      </div>
    `;

    // Display stored images
    if (storedImages.size > 0) {
      html += '<div style="margin-bottom: 15px;"><strong>Images:</strong></div>';
      for (const [alias, imageData] of storedImages) {
        html += `
          <div style="margin-bottom: 10px; padding: 8px; background: rgba(255,255,255,0.1); border-radius: 4px;">
            <div style="margin-bottom: 5px;">
              <strong>${alias}</strong> (Frame #${imageData.frameNumber})<br>
              <small>${imageData.dimensions.width}x${imageData.dimensions.height} - ${new Date(imageData.timestamp).toLocaleTimeString()}</small>
            </div>
            <img src="${imageData.base64}" style="max-width: 100%; height: auto; border: 1px solid #666; border-radius: 4px;">
          </div>
        `;
      }
    }

    // Display comparison history
    if (comparisonHistory.length > 0) {
      html += '<div style="margin-bottom: 10px;"><strong>Comparisons:</strong></div>';
      // Show all comparisons, not just recent ones
      comparisonHistory.forEach((comparison) => {
        const result = comparison.result;
        const diffThreshold = config.diffThreshold ?? 5;
        const diffColor = result.percentageDiff > diffThreshold ? '#ff4444' : '#44ff44';

        html += `
          <div style="margin-bottom: 8px; padding: 6px; background: rgba(255,255,255,0.05); border-radius: 4px; border-left: 3px solid ${diffColor};">
            <div style="font-weight: bold;">${comparison.image1} vs ${comparison.image2}</div>
            <div style="color: ${diffColor};">${result.percentageDiff.toFixed(2)}% difference</div>
            <div style="font-size: 10px; color: #ccc;">
              Frame #${comparison.frameNumber} - ${new Date(comparison.timestamp).toLocaleTimeString()}<br>
              ${result.numDiffPixels} pixels changed (${result.comparisonTime.toFixed(1)}ms)
            </div>
          </div>
        `;
      });
    }

    content.innerHTML = html;
  }

  /**
   * Starts monitoring for UI settling timeout.
   * If the UI doesn't settle within maxWaitDuration, triggers mouse movements to generate frames.
   */
  function startSettleTimeoutMonitoring() {
    if (mouseMoveTriggerTimeoutId) {
      clearTimeout(mouseMoveTriggerTimeoutId);
    }

    mouseMoveTriggerTimeoutId = setTimeout(() => {
      handleSettleTimeout();
    }, config.maxWaitDuration);

    if (config.debug) {
      console.log(`Started settle timeout monitoring (${config.maxWaitDuration}ms)`);
    }
  }

  /**
   * Clears the settle timeout monitoring.
   */
  function clearSettleTimeoutMonitoring() {
    if (mouseMoveTriggerTimeoutId) {
      clearTimeout(mouseMoveTriggerTimeoutId);
      mouseMoveTriggerTimeoutId = null;

      if (config.debug) {
        console.log('Cleared settle timeout monitoring');
      }
    }
  }

  /**
   * Starts monitoring for overall comparison timeout.
   * If no significant change is detected within maxWaitDuration, stops the comparison.
   */
  function startComparisonTimeoutMonitoring() {
    if (comparisonTimeoutId) {
      clearTimeout(comparisonTimeoutId);
    }

    comparisonTimeoutId = setTimeout(() => {
      handleComparisonTimeout();
    }, config.maxWaitDuration);

    if (config.debug) {
      console.log(`Started comparison timeout monitoring (${config.maxWaitDuration}ms)`);
    }
  }

  /**
   * Clears the comparison timeout monitoring.
   */
  function clearComparisonTimeoutMonitoring() {
    if (comparisonTimeoutId) {
      clearTimeout(comparisonTimeoutId);
      comparisonTimeoutId = null;

      if (config.debug) {
        console.log('Cleared comparison timeout monitoring');
      }
    }
  }

  /**
   * Handles the case when UI doesn't settle within the maximum wait duration.
   * Triggers a single mouse movement to generate more frames for comparison.
   */
  async function handleSettleTimeout() {
    if (!significantChangeDetected || hasSettledFrame) {
      // UI has already settled or no significant change was detected
      return;
    }

    if (config.debug) {
      console.log('UI settle timeout reached. Triggering request new frame to generate frames.');
    }

    if (
      window.browserController &&
      typeof window.browserController.requestNewFrame === 'function'
    ) {
      try {
        await window.browserController.requestNewFrame();
        if (config.debug) {
          console.log('requestNewFrame triggered successfully via browser controller');
        }
      } catch (error) {
        console.error('Error triggering requestNewFrame:', error);
      }
    } else {
      console.error(
        'Cannot trigger requestNewFrame: browserController.requestNewFrame not available',
      );
    }

    // Clear the timeout ID since we only do this once
    mouseMoveTriggerTimeoutId = null;
  }

  /**
   * Handles the case when no significant change is detected within the maximum wait duration.
   * Stops the comparison process and cleans up.
   */
  function handleComparisonTimeout() {
    if (significantChangeDetected || hasSettledFrame) {
      // Significant change was detected or UI has settled, no need to timeout
      return;
    }

    if (config.debug) {
      console.log(
        `Comparison timeout reached (${config.maxWaitDuration}ms). No significant change detected. Stopping comparison.`,
      );
    }

    // Log performance statistics if frames were processed
    if (frameCount > 0) {
      const avgConversionTime = totalConversionTime / frameCount;
      const avgComparisonTime = totalComparisonTime / frameCount;

      console.log(`
        Performance Statistics (Timeout):
        - Total frames processed: ${frameCount}
        - Average conversion time: ${avgConversionTime.toFixed(2)} ms
        - Average comparison time: ${avgComparisonTime.toFixed(2)} ms
        - Total conversion time: ${totalConversionTime.toFixed(2)} ms
        - Total comparison time: ${totalComparisonTime.toFixed(2)} ms
        - Pixel sampling rate: ${config.sampling ?? 1}x (processed ${Math.round(100 / (config.sampling ?? 1))}% of pixels)
      `);
    }

    // Clean up the comparison
    cleanupComparison();

    // Clear the timeout ID
    comparisonTimeoutId = null;
  }

  /**
   * Starts the screenshot comparison process.
   * Initiates frame capture from the screen cropper.
   * The comparison runs until a significant change is detected and then the UI has settled.
   * Uses maxWaitDuration as a timeout to prevent infinite comparison.
   */
  function triggerScreenshotComparison(): Promise<ComparisonResult> {
    return new Promise((resolve, reject) => {
      if (!isInitialized) {
        console.log('Trigger ignored: not initialized.');
        reject(new Error('Captcha detector not initialized'));
        return;
      }
      if (isComparing) {
        if (config.debug) {
          console.log('Trigger ignored: already comparing.');
        }
        reject(new Error('Comparison already in progress'));
        return;
      }

      if (config.debug) {
        console.log('Trigger received, starting comparison loop');
      }

      // Reset state variables
      isComparing = true;
      significantChangeDetected = false;
      consecutiveSettledFramesCount = 0;
      frameSendingPaused = false;
      settledFrameData = null;
      hasSettledFrame = false;
      // Clear any existing timeout monitoring
      clearSettleTimeoutMonitoring();
      clearComparisonTimeoutMonitoring();
      // Reset frameCount for new comparison session
      frameCount = 0;
      totalConversionTime = 0;
      totalComparisonTime = 0;

      if (
        window.screenCropper &&
        typeof window.screenCropper.startCapturingForCaptchaDetector === 'function'
      ) {
        // Use streamed frames approach
        if (config.debug) {
          console.log('Using streamed frames for comparison');
        }
        window.screenCropper.startCapturingForCaptchaDetector();

        // Start the overall comparison timeout to prevent infinite comparison
        startComparisonTimeoutMonitoring();

        // For now, resolve with a basic result - in practice this would be resolved when comparison completes
        resolve({
          percentageDiff: 0,
          numDiffPixels: 0,
          comparisonTime: 0,
        });
      } else {
        console.error(
          'Cannot start comparison: screenCropper or startCapturingForCaptchaDetector not available.',
        );
        isComparing = false;
        reject(new Error('Screen cropper not available'));
      }
    });
  }

  /**
   * Cleans up the comparison process and resets state.
   * Called when comparison is complete or times out.
   */
  function cleanupComparison() {
    isComparing = false;
    significantChangeDetected = false;

    // Clear timeout monitoring
    clearSettleTimeoutMonitoring();
    clearComparisonTimeoutMonitoring();

    // Stop capturing frames
    if (
      window.screenCropper &&
      typeof window.screenCropper.stopCapturingForCaptchaDetector === 'function'
    ) {
      window.screenCropper.stopCapturingForCaptchaDetector();
    }

    // Resume frame sending if it was paused
    if (
      frameSendingPaused &&
      window.screenCropper &&
      typeof window.screenCropper.resumeFrameSending === 'function'
    ) {
      window.screenCropper.resumeFrameSending();
      frameSendingPaused = false;

      if (config.debug) {
        console.log('Resumed frame sending during cleanup');
      }
    }

    // Reset comparison counters and state
    // Note: frameCount, totalConversionTime, and totalComparisonTime are reset at the start of each comparison session
    consecutiveSettledFramesCount = 0;
    lastImageData = null; // Reset lastImageData to ensure fresh comparison on next trigger
    lastImageAlias = null; // Reset last image alias
    settledFrameData = null; // Reset settled frame data
    hasSettledFrame = false; // Reset settled frame flag

    // Note: We don't clear debug storage here anymore
    // Debug data should persist across comparison sessions
    // Only clear it when explicitly requested via clearDebugData()

    if (config.debug) {
      console.log('Comparison state cleaned up');
    }
  }

  /**
   * Compares two image data objects to calculate the percentage of different pixels.
   * Uses the screenshot comparison utility module.
   *
   * @param img1 - First image to compare (ImageData or {data, width, height})
   * @param img2 - Second image to compare (ImageData or {data, width, height})
   * @returns Percentage of pixels that differ between the images
   */
  function compareScreenshots(img1: IImageData, img2: IImageData): number {
    if (!img1 || !img2) {
      console.error('Unable to compare screenshots: missing image data');
      return 0;
    }

    if (!window.screenshotComparisonUtils) {
      console.error('Screenshot comparison utilities not loaded. Cannot compare screenshots.');
      return 0;
    }

    try {
      const result = window.screenshotComparisonUtils.compareScreenshots(img1, img2, {
        sampling: config.sampling,
      });
      return result.percentageDiff;
    } catch (e) {
      console.error('Error using screenshot comparison utilities:', e);
      return 0;
    }
  }

  /**
   * Runs TensorFlow detection on a frame and updates the crop box if a captcha is detected.
   * This is called when the UI has settled to get accurate bounding boxes.
   *
   * @param rgbaBuffer - RGBA buffer containing the frame data
   * @param dimensions - The dimensions of the frame
   * @returns The detection results or null if no detection was made
   */
  async function runTensorFlowDetection(
    rgbaBuffer: Uint8Array,
    dimensions: { width: number; height: number },
  ): Promise<DetectionResults | null> {
    let detectionResults: DetectionResults | null = null;
    let updatedBoundingBox: BoundingBox | null = null;

    if (!window.tfCaptchaDetector) {
      console.log('TensorFlow captcha detector not available');
      return null;
    }

    try {
      console.log('Running TensorFlow object detection on settled frame...');
      detectionResults = await window.tfCaptchaDetector.detectObjects(rgbaBuffer, dimensions);

      if (detectionResults && detectionResults.detections.length > 0) {
        updatedBoundingBox = window.tfCaptchaDetector.findBestCaptchaBoundingBox(
          detectionResults.detections,
        );
        console.log('Found TF captcha bounding box:', updatedBoundingBox);
        // try to update the crop box with the new bounding box from TensorFlow
        if (
          window.screenCropper &&
          typeof window.screenCropper.updateCropBox === 'function' &&
          updatedBoundingBox
        ) {
          const bboxWithPadding = padCropBox(updatedBoundingBox); // cspell:disable-line
          console.log(
            'Updating crop box with TensorFlow bounding box containing padding:', // cspell:disable-line
            bboxWithPadding, // cspell:disable-line
          );
          window.screenCropper.updateCropBox(bboxWithPadding); // cspell:disable-line
        }
      } else {
        console.log('No objects detected by TensorFlow model');
      }

      return detectionResults;
    } catch (error) {
      console.error('Error during TensorFlow detection:', error);
      return null;
    }
  }

  /**
   * Pauses frame sending to the frontend.
   * Called when a significant change is detected to stop sending frames while UI is changing.
   */
  function pauseFrameSending() {
    if (window.screenCropper && typeof window.screenCropper.pauseFrameSending === 'function') {
      window.screenCropper.pauseFrameSending();
      frameSendingPaused = true;
      if (config.debug) {
        console.log('Frame sending paused');
      }
    }
  }

  /**
   * Notifies the application when captcha has been solved (no captcha detected after UI settled).
   * Allows the workflow to proceed with LLM analysis of the current form state.
   *
   * @param differencePercentage - The percentage of pixels that differ
   * @param source - Source of the notification (e.g., 'redirect', 'ui_settled')
   */
  async function notifyCaptchaSolved(
    differencePercentage: number,
    source: string = 'ui_settled',
  ): Promise<void> {
    // Create message data for captcha solved notification
    const messageData = {
      type: 'CAPTCHA_SOLVED',
      differencePercentage,
      timestamp: new Date().toISOString(),
      boundingBox: null, // We'll update the bounding box later when the UI has settled
      source: source, // Include the source of the notification
    };

    // Type-safe access to window.__captchaSolved__
    const captchaSolvedCallback = (window as any).__captchaSolved__;
    if (typeof captchaSolvedCallback === 'function') {
      try {
        captchaSolvedCallback(JSON.stringify(messageData));
        if (config.debug)
          console.log(
            `Called binding to notify captcha solved (${differencePercentage.toFixed(2)}%, source: ${source})`,
          );
      } catch (bindingError) {
        console.error('Error calling captcha solved binding:', bindingError);
      }
    } else {
      console.error('Binding function __captchaSolved__ not found! Cannot notify captcha solved.');
    }
  }

  function padCropBox(cropBox: BoundingBox, padding: number = 20): BoundingBox {
    const x = Math.max(0, cropBox.x - padding);
    const y = Math.max(0, cropBox.y - padding);

    const xPadUsed = cropBox.x - x;
    const yPadUsed = cropBox.y - y;

    const width = cropBox.width + xPadUsed + padding;
    const height = cropBox.height + yPadUsed + padding;

    return { x, y, width, height };
  }

  /**
   * Handles the beforeunload event which is triggered when a page is about to be unloaded.
   * This can happen when a page redirects after captcha completion.
   * @param event - The beforeunload event
   */
  async function handleBeforeUnload(_event: BeforeUnloadEvent): Promise<void> {
    console.log('Page unload detected, notifying captcha solved...');

    await notifyCaptchaSolved(0, 'redirect');

    // Clean up our state
    cleanupComparison();
  }

  /**
   * Cleans up resources and resets the captcha detector state.
   * Stops any ongoing comparison and frame capture.
   */
  function cleanup() {
    if (!isInitialized) {
      console.log('Captcha detector cleanup skipped: not initialized');
      return;
    }

    try {
      console.log('Starting captcha detector cleanup');

      // Use the cleanupComparison function to handle comparison-related cleanup
      cleanupComparison();

      // Additional cleanup specific to the entire detector
      isProcessingComparison = false;
      isInitialized = false;

      // Remove event listeners
      window.removeEventListener('beforeunload', handleBeforeUnload);
      console.log('Captcha detector stopped and cleaned up successfully');
    } catch (err) {
      console.error('Error during captcha detector cleanup:', err);
      isInitialized = false;
    }
  }

  function loadDependencies(): Promise<void> {
    return new Promise((resolve, reject) => {
      // Check if pixelmatch is available
      if (typeof window.pixelmatch !== 'function') {
        if (config.debug) console.log('Pixelmatch library not available, using fallback...');
        // For TypeScript conversion, we'll use a simple fallback instead of bundled code
        // In production, pixelmatch should be loaded separately
        (window as any).pixelmatch = function (
          img1: Uint8ClampedArray,
          img2: Uint8ClampedArray,
          output: Uint8Array | null,
          _width: number,
          _height: number,
          options?: any,
        ): number {
          console.warn(
            'Using fallback pixelmatch implementation - load pixelmatch library for better performance',
          );
          // Simple pixel difference count fallback
          let diffCount = 0;
          const threshold = options?.threshold || 0.1;

          for (let i = 0; i < img1.length; i += 4) {
            const r1 = img1[i],
              g1 = img1[i + 1],
              b1 = img1[i + 2],
              a1 = img1[i + 3];
            const r2 = img2[i],
              g2 = img2[i + 1],
              b2 = img2[i + 2],
              a2 = img2[i + 3];

            const dr = Math.abs(r1 - r2) / 255;
            const dg = Math.abs(g1 - g2) / 255;
            const db = Math.abs(b1 - b2) / 255;
            const da = Math.abs(a1 - a2) / 255;

            const diff = (dr + dg + db + da) / 4;

            if (diff > threshold) {
              diffCount++;
              if (output) {
                output[i] = 255; // Red
                output[i + 1] = 0; // Green
                output[i + 2] = 0; // Blue
                output[i + 3] = 255; // Alpha
              }
            } else if (output) {
              output[i] = img1[i];
              output[i + 1] = img1[i + 1];
              output[i + 2] = img1[i + 2];
              output[i + 3] = img1[i + 3];
            }
          }

          return diffCount;
        };

        if (config.debug) console.log('Fallback pixelmatch function defined');
      } else if (config.debug) {
        console.log('pixelmatch already defined globally');
      }

      if (!window.screenshotComparisonUtils) {
        if (config.debug)
          console.log('Waiting for screenshot comparison utilities to be loaded...');
        let checkCount = 0;
        const maxChecks = 20;

        const checkInterval = setInterval(() => {
          checkCount++;
          if (window.screenshotComparisonUtils) {
            if (config.debug) console.log('Screenshot comparison utilities loaded successfully');
            clearInterval(checkInterval);
            resolve();
          } else if (checkCount >= maxChecks) {
            clearInterval(checkInterval);
            console.error('Timed out waiting for screenshot comparison utilities to load');
            reject('Timed out waiting for screenshot comparison utilities');
          }
        }, 250);
      } else {
        if (config.debug) console.log('Screenshot comparison utilities already loaded');
        resolve();
      }
    });
  }

  window.addEventListener('message', (event) => {
    if (!event.data || typeof event.data !== 'object') return;

    switch (event.data.type) {
      case 'START_CAPTCHA_MONITORING':
        console.log('Received START_CAPTCHA_MONITORING message');
        if (!isInitialized) {
          loadDependencies()
            .then(() => {
              initialize(event.data.config || {});
            })
            .catch((error) => {
              console.error('Failed to initialize captcha detector:', error);
            });
        } else {
          if (config.debug) console.log('Already initialized, ignoring START message.');
        }
        break;

      case 'STOP_CAPTCHA_MONITORING':
        console.log('Received STOP_CAPTCHA_MONITORING message');
        cleanup();
        break;
    }
  });

  // Type-safe window assignment
  window.captchaDetector = {
    initialize,
    cleanup,
    triggerScreenshotComparison,
    getConfig: (): CaptchaDetectorConfig => ({ ...config }),
    updateConfig: (newConfig: Partial<CaptchaDetectorConfig>): void => {
      Object.assign(config, newConfig);
      console.log('Configuration updated:', newConfig);
    },
  };

  console.log(`Captcha detector script loaded in ${window.location.href}`);
})();
