import {
  RemoteBrowserService as BrowserService,
  BrowserSession,
} from '../adapters/BrowserDataAdapter';

/**
 * Chrome debugger info interface for local Chrome connection
 */
interface ChromeDebuggerInfo {
  webSocketDebuggerUrl: string;
}

/**
 * Local browser service implementation for connecting to locally running Chrome
 * Requires Chrome to be manually started with debugging enabled
 */
export class LocalBrowserService implements BrowserService {
  private readonly debugPort: number;
  private readonly debugHost: string;

  constructor(debugPort: number = 9222, debugHost: string = 'localhost') {
    this.debugPort = debugPort;
    this.debugHost = debugHost;
  }

  /**
   * Create a new browser session by connecting to local Chrome
   * Requires Chrome to be manually started with debugging enabled
   */
  async createSession(): Promise<BrowserSession> {
    console.log('→ Connecting to local Chrome instance');

    try {
      const wsEndpoint = await this.tryConnectToExistingChrome();
      if (wsEndpoint) {
        console.log('✓ Successfully connected to existing Chrome instance');
        return {
          wsEndpoint,
          sessionId: 'local-chrome-session',
        };
      }
    } catch (error) {
      // Connection failed
    }

    // No existing Chrome instance found
    throw new Error(
      `No Chrome instance found running with debugging enabled on port ${this.debugPort}. ` +
        `Please manually start Chrome with:\n\n` +
        `chrome --remote-debugging-port=${this.debugPort} --remote-allow-origins=* --auto-accept-this-tab-capture\n\n` +
        `Then try again.`,
    );
  }

  /**
   * Try to connect to an existing Chrome instance
   */
  private async tryConnectToExistingChrome(): Promise<string | null> {
    const debugUrl = `http://${this.debugHost}:${this.debugPort}/json/version`;
    const response = await fetch(debugUrl);

    if (!response.ok) {
      return null;
    }

    const debugInfo = (await response.json()) as ChromeDebuggerInfo;
    return debugInfo.webSocketDebuggerUrl || null;
  }

  /**
   * Get an existing browser session
   * For local Chrome, this is the same as creating a session since we always connect to the same instance
   */
  async getSession(sessionId: string): Promise<BrowserSession> {
    console.log(`→ Getting local Chrome session: ${sessionId}`);
    return this.createSession();
  }

  /**
   * Close a browser session
   * Since we don't launch Chrome automatically, this is a no-op
   */
  async closeSession(sessionId: string): Promise<void> {
    console.log(`→ Local Chrome session ${sessionId} close requested`);
    console.log('Chrome instance is externally managed - no action taken');
  }
}
