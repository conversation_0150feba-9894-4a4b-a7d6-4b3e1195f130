import { describe, expect, it } from 'vitest';
import { MockBrowserDataAdapter } from './mocks/MockBrowserDataAdapter';
import { <PERSON><PERSON> } from 'hono/utils/cookie';

/**
 * Test demonstrating how the service abstraction makes testing easier
 * This test focuses purely on the adapter behavior without any CDP complexity
 */
describe('BrowserDataAdapter Tests', () => {
  it('should handle empty initial state', async () => {
    // Arrange
    const adapter = new MockBrowserDataAdapter();

    // Act & Assert
    expect(await adapter.getCookies()).toEqual([]);
    expect(await adapter.getLocalStorageData()).toEqual({});
    expect(await adapter.getSessionStorageData()).toEqual({});
  });

  it('should store and retrieve cookies', async () => {
    // Arrange
    const adapter = new MockBrowserDataAdapter();
    const testCookies: <PERSON>ie[] = [
      {
        name: 'test_cookie',
        value: 'test_value',
        domain: 'example.com',
        path: '/',
        expires: '1234567890',
        secure: 'true',
        httpOnly: 'false',
        sameSite: 'Lax' as const,
        size: '20',
        session: 'false',
        priority: 'Medium' as const,
        sameParty: 'false',
        sourceScheme: 'Secure' as const,
      },
    ];

    // Act
    await adapter.setCookies(testCookies);
    const retrievedCookies = await adapter.getCookies();

    // Assert
    expect(retrievedCookies).toEqual(testCookies);
  });

  it('should store and retrieve localStorage data', async () => {
    // Arrange
    const adapter = new MockBrowserDataAdapter();
    const testData = {
      user_id: '12345',
      theme: 'dark',
      settings: JSON.stringify({ notifications: true }),
    };

    // Act
    await adapter.setLocalStorageData(testData);
    const retrievedData = await adapter.getLocalStorageData();

    // Assert
    expect(retrievedData).toEqual(testData);
  });

  it('should store and retrieve sessionStorage data', async () => {
    // Arrange
    const adapter = new MockBrowserDataAdapter();
    const testData = {
      session_token: 'abc123',
      temp_data: 'temporary_value',
    };

    // Act
    await adapter.setSessionStorageData(testData);
    const retrievedData = await adapter.getSessionStorageData();

    // Assert
    expect(retrievedData).toEqual(testData);
  });

  it('should handle complex data scenarios', async () => {
    // Arrange
    const adapter = new MockBrowserDataAdapter();

    const cookies = [
      {
        name: 'session',
        value: 'session_123',
        domain: 'app.com',
        path: '/',
        expires: '1234567890',
        secure: 'true',
        httpOnly: 'true',
        sameSite: 'Strict' as const,
        size: '15',
        session: 'false',
      },
    ];

    const localStorage = {
      user_preferences: JSON.stringify({
        language: 'en',
        timezone: 'UTC',
        theme: 'dark',
      }),
      last_login: '2024-01-01T00:00:00Z',
    };

    const sessionStorage = {
      current_page: 'dashboard',
      scroll_position: '150',
    };

    // Act
    await adapter.setCookies(cookies);
    await adapter.setLocalStorageData(localStorage);
    await adapter.setSessionStorageData(sessionStorage);

    // Assert
    expect(await adapter.getCookies()).toEqual(cookies);
    expect(await adapter.getLocalStorageData()).toEqual(localStorage);
    expect(await adapter.getSessionStorageData()).toEqual(sessionStorage);
  });

  it('should handle data updates correctly', async () => {
    // Arrange
    const adapter = new MockBrowserDataAdapter();

    const initialData = { key1: 'value1', key2: 'value2' };
    const updatedData = { key1: 'updated_value1', key3: 'value3' };

    // Act
    await adapter.setLocalStorageData(initialData);
    await adapter.setLocalStorageData(updatedData);
    const finalData = await adapter.getLocalStorageData();

    // Assert - should completely replace, not merge
    expect(finalData).toEqual(updatedData);
    expect(finalData).not.toHaveProperty('key2');
  });
});
