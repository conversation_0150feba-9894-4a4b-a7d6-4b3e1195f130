/**
 * Test for password toggle functionality in form generation
 */

import { describe, it, expect } from 'vitest';
import { generateFloatingLabelField } from '../../src/form-generation/components/fields/floating-label-field';
import { FormField } from '../../src/form-generation/types/form-interfaces';

describe('Password Toggle Functionality', () => {
  const mockPasswordField: FormField = {
    id: 'password',
    order: 1,
    label: 'Password',
    isLikelyDropdown: false,
    fieldControlType: 'password',
    actiontype: 'fill',
    name: 'password',
    checked: false,
    options: null,
  };

  const mockTextField: FormField = {
    id: 'email',
    order: 1,
    label: 'Email',
    isLikelyDropdown: false,
    fieldControlType: 'text',
    actiontype: 'fill',
    name: 'email',
    checked: false,
    options: null,
  };

  it('should generate password field with toggle checkbox', () => {
    const result = generateFloatingLabelField(mockPasswordField);
    
    // Should contain the password input
    expect(result).toContain('type="password"');
    expect(result).toContain('id="password"');
    expect(result).toContain('name="password"');
    
    // Should contain the toggle checkbox
    expect(result).toContain('password-toggle-container');
    expect(result).toContain('password-toggle-checkbox');
    expect(result).toContain('id="password-toggle"');
    
    // Should contain the toggle label
    expect(result).toContain('password-toggle-label');
    expect(result).toContain('Show password');
    
    // Should contain the JavaScript function call
    expect(result).toContain('togglePasswordVisibility(\'password\', this)');
  });

  it('should not generate toggle for non-password fields', () => {
    const result = generateFloatingLabelField(mockTextField);
    
    // Should contain the text input
    expect(result).toContain('type="text"');
    expect(result).toContain('id="email"');
    
    // Should NOT contain password toggle elements
    expect(result).not.toContain('password-toggle-container');
    expect(result).not.toContain('password-toggle-checkbox');
    expect(result).not.toContain('Show password');
    expect(result).not.toContain('togglePasswordVisibility');
  });

  it('should generate unique toggle IDs for multiple password fields', () => {
    const passwordField1: FormField = {
      ...mockPasswordField,
      id: 'password1',
      name: 'password1',
    };
    
    const passwordField2: FormField = {
      ...mockPasswordField,
      id: 'password2',
      name: 'password2',
    };

    const result1 = generateFloatingLabelField(passwordField1);
    const result2 = generateFloatingLabelField(passwordField2);
    
    // Should have unique toggle IDs
    expect(result1).toContain('id="password1-toggle"');
    expect(result2).toContain('id="password2-toggle"');
    
    // Should have unique function calls
    expect(result1).toContain('togglePasswordVisibility(\'password1\', this)');
    expect(result2).toContain('togglePasswordVisibility(\'password2\', this)');
  });

  it('should maintain floating label structure for password fields', () => {
    const result = generateFloatingLabelField(mockPasswordField);
    
    // Should maintain the floating label structure
    expect(result).toContain('input-container');
    expect(result).toContain('floating-input-wrapper');
    expect(result).toContain('floating-input');
    expect(result).toContain('floating-label');
    expect(result).toContain('for="password"');
    expect(result).toContain('>Password</label>');
  });
});
