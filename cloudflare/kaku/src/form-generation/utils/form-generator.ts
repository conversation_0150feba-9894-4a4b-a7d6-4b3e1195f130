/**
 * Isolated form generation utilities
 */

import { htmxFormGenerator } from '../htmx-generator';
import { ClassificationResult, ScreenClass } from '../types';
import { ExtractionResult } from '../types/form-interfaces';

/**
 * Generate HTMX form markup from FormVisionResult
 * This is an isolated function that can be called independently
 */
export function generateHtmxForm(
  extractionResult: ExtractionResult,
  classificationResult: ClassificationResult,
): string {
  return htmxFormGenerator.generateForm(extractionResult, classificationResult);
}

/**
 * Determine if static description is required
 */
export function needsStaticDescription(extractionResult?: ExtractionResult): {
  shouldShowStaticDescription: boolean;
  staticCredentialText: string;
} {
  if (!extractionResult) {
    return { shouldShowStaticDescription: false, staticCredentialText: '' };
  }

  const keywords = ['password', 'email', 'username', 'phone number', 'phone'];

  const shouldShowStaticDescription = extractionResult.controls.fields.some((control) =>
    keywords.some((keyword) => control.label.toLowerCase().includes(keyword)),
  );

  const staticCredentialText =
    extractionResult.controls.fields.length <= 1
      ? extractionResult.controls.fields[0].label.toLowerCase()
      : 'credentials';

  return { shouldShowStaticDescription, staticCredentialText };
}