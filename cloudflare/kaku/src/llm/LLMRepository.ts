import { LLMResponse } from "./types/llm-response";
import {LLMRequest} from "./types/llm-request";
import { DetectPageStateChangeLlmRequest } from "./types/detect-page-state-change-llm-request";

export interface LLMRepository {
    getLLMResponse(llmRequest: LLMRequest): Promise<LLMResponse>

    detectStateChangeFromPreviousFormVisionResult(detectPageStateChangeLlmRequest: DetectPageStateChangeLlmRequest): Promise<LLMResponse>
}