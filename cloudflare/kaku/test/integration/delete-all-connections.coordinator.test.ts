import { env } from 'cloudflare:test';
import { describe, it, expect, beforeEach } from 'vitest';
import { CoordinatorDO } from '../../src/coordinator/coordinator-do';
import { PlatformTypes } from '../../src/ui/constants';

/**
 * Focused tests for CoordinatorDO.deleteAllConnections
 */

describe('CoordinatorDO deleteAllConnections', () => {
  let coordinator: DurableObjectStub<CoordinatorDO>;
  let testCounter = 0;

  beforeEach(async () => {
    testCounter++;
    const coordinatorId = env.CoordinatorDO.idFromName(`test-coordinator-delete-all-${testCounter}`);
    coordinator = env.CoordinatorDO.get(coordinatorId) as DurableObjectStub<CoordinatorDO>;
  });

  it('deletes all agent DOs and clears state', async () => {
    const userId = `u_user_${testCounter}`;
    const platforms: PlatformTypes[] = ['facebook', 'github', 'google'];

    const createdAgents: string[] = [];
    for (const p of platforms) {
      const { linkId } = await coordinator.createLink(p, userId);
      createdAgents.push(linkId);
      await coordinator.markPlatformConnected(linkId);
    }

    const result = await coordinator.deleteAllConnections();

    expect(result).toBeDefined();
    expect(Array.isArray(result.deletedAgents)).toBe(true);
    expect(result.deletedAgents.length).toBeGreaterThanOrEqual(createdAgents.length);
    expect(result.platformsCleared).toBeGreaterThan(0);

    const state = (await coordinator.state) as any;
    expect(state.platforms.length).toBe(0);
  });

  it('is idempotent when called on an already cleared coordinator', async () => {
    const result1 = await coordinator.deleteAllConnections();
    expect(result1.platformsCleared).toBe(0);
    expect(result1.deletedAgents.length).toBe(0);

    const result2 = await coordinator.deleteAllConnections();
    expect(result2.platformsCleared).toBe(0);
    expect(result2.deletedAgents.length).toBe(0);
  });
});

