import { Context, Hono } from 'hono';
import { <PERSON><PERSON><PERSON><PERSON> } from '../../common/types';
import { LINK_ID, PLATFORM_ID, USER_ID } from '../constants';
import { linkValidationMiddleware } from '../middleware/link-validation';
import { platformDetails, PlatformTypes } from '../../ui/constants';
import {
  DashboardLayoutCard,
  ErrorPageLayout,
  LayoutWithCard,
  RetryLimitExceededErrorContent,
} from '../../ui';
import { capitalize } from '../../workflow/utils/helpers';

const app = new Hono<KakuApp>();

// Reset link endpoint
app.get(`/app/:${LINK_ID}/reset`, async (c) => {
  try {
    const linkId = c.req.param(LINK_ID);
    const userId = c.req.query(USER_ID);
    const platformId = c.req.query(PLATFORM_ID) as PlatformTypes;

    if (!userId || !platformId) {
      return c.json({ error: 'Missing userId or platformId query parameters' }, 400);
    }

    const detail = platformDetails[platformId];
    if (!detail) {
      return c.json({ error: 'Invalid platformId' }, 400);
    }

    const coordinator = c.env.CoordinatorDO.idFromName(userId);
    const stub = c.env.CoordinatorDO.get(coordinator);

    // Use the generic reset method that combines revoke and create
    const resetResult = await stub.resetLink(linkId, userId, platformId);

    if (!resetResult) {
      return c.json({ error: 'Link not found or not active' }, 404);
    }

    // Redirect to the new link URL
    return c.redirect(resetResult.newUrl, 302);
  } catch (error) {
    return c.html(
      ErrorPageLayout({
        title: 'Retry Limit Exceeded',
        children: RetryLimitExceededErrorContent(),
      }),
      429,
    );
  }
});

// Main connection page
app.get(`/:${PLATFORM_ID}/:${LINK_ID}`, linkValidationMiddleware, async (c) => {
  const userId = c.get(USER_ID);
  const linkId = c.get(LINK_ID);
  const platformId = c.get(PLATFORM_ID) as PlatformTypes;

  const connectionDOName = `${linkId}`;
  const agent = c.env.Connections.idFromName(connectionDOName);
  const stub = c.env.Connections.get(agent);
  await stub.setName(connectionDOName);
  if (!isPreviewRequest(c)) {
    c.executionCtx.waitUntil(stub.eagerlyInitializeResources(platformId, userId, linkId));
  }

  return c.html(DashboardLayoutCard(userId, linkId, platformId, capitalize(platformId)));
});

// Connection flow page
app.get(`/:${PLATFORM_ID}/:${LINK_ID}/flow`, linkValidationMiddleware, (c) => {
  const serviceId = c.get(PLATFORM_ID);
  const linkId = c.get(LINK_ID);

  const wsEndpoint = `${c.env.KAKU_WS_ENDPOINT}/v1/agents/connections/${linkId}`;
  return c.html(
    LayoutWithCard(
      { wsEndpoint, linkId },
      {
        serviceId: serviceId,
        serviceName: capitalize(serviceId),
        serviceLogo: `/fb.png`,
        serviceTitle: 'Testing Forms',
        formTitle: 'Testing Forms',
        serviceDescription: 'Testing Forms',
        liveViewToggleText: 'Show Live View',
        loadingText: 'Processing...',
      },
    ),
  );
});



const PREVIEW_BOT_UA: RegExp[] = [
  /Slackbot/i,
  /Slack-ImgProxy/i,
  /Discordbot/i,
  /TelegramBot/i,
  /WhatsApp/i,
  /Twitterbot/i,
  /facebookexternalhit/i,
  /Facebot/i,
  /LinkedInBot/i,
  /SkypeUriPreview/i,
  /Pinterestbot/i,
  /Snapchat/i,
  /DuckDuckBot/i,
  /bingbot/i,
  /Applebot/i,
  /Googlebot/i,
  /bitlybot/i,
  /Embedly/i,
  /PocketParser/i,
  /Google-PageRenderer/i,
]

export function isPreviewRequest(c: Context) {
  const method = c.req.method
  if (method === 'HEAD') return true

  const ua = c.req.header('user-agent') ?? ''
  if (PREVIEW_BOT_UA.some((re) => re.test(ua))) return true

  // Browser/infra hints for prefetch/prerender/preview
  const secPurpose = c.req.header('sec-purpose') || c.req.header('purpose') || ''
  if (/\b(prefetch|prerender|preview)\b/i.test(secPurpose)) return true

  // Some clients set non-standard hints:
  // X-Purpose: preview  |  X-Moz: prefetch
  const xPurpose = c.req.header('x-purpose') || c.req.header('x-moz') || ''
  return /\bpreview|prefetch\b/i.test(xPurpose);
}


export { app as AppHandler };
