import { html } from 'hono/html';

export const LoginForm = () => {
  return html`
    <!DOCTYPE html>
    <html lang="en">
      <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Simulated Login - Kaku</title>
        <style>
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }
          
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
          }
          
          .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
          }
          
          .glass-effect {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
            border-radius: 1rem;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            padding: 2rem;
            width: 100%;
            max-width: 28rem;
          }
          
          .text-center { text-align: center; }
          .mb-8 { margin-bottom: 2rem; }
          .mb-6 { margin-bottom: 1.5rem; }
          .mb-4 { margin-bottom: 1rem; }
          .mb-2 { margin-bottom: 0.5rem; }
          .mt-6 { margin-top: 1.5rem; }
          .ml-2 { margin-left: 0.5rem; }
          
          .text-3xl { font-size: 1.875rem; }
          .text-sm { font-size: 0.875rem; }
          .font-bold { font-weight: 700; }
          .font-semibold { font-weight: 600; }
          .font-medium { font-weight: 500; }
          
          .text-white { color: white; }
          .text-white-80 { color: rgba(255, 255, 255, 0.8); }
          .text-white-60 { color: rgba(255, 255, 255, 0.6); }
          .text-purple-600 { color: #7c3aed; }
          
          .space-y-6 > * + * { margin-top: 1.5rem; }
          
          .block { display: block; }
          .flex { display: flex; }
          .items-center { align-items: center; }
          .justify-between { justify-content: space-between; }
          .w-full { width: 100%; }
          
          input[type="email"], input[type="password"] {
            width: 100%;
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            font-size: 1rem;
            transition: all 0.2s;
          }
          
          input[type="email"]::placeholder, input[type="password"]::placeholder {
            color: rgba(255, 255, 255, 0.6);
          }
          
          input[type="email"]:focus, input[type="password"]:focus {
            outline: none;
            ring: 2px solid rgba(255, 255, 255, 0.5);
            border-color: transparent;
          }
          
          input[type="checkbox"] {
            border-radius: 0.25rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
          }
          
          button {
            width: 100%;
            background: white;
            color: #7c3aed;
            font-weight: 600;
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            border: none;
            cursor: pointer;
            transition: all 0.2s;
            transform: scale(1);
          }
          
          button:hover {
            background: rgba(255, 255, 255, 0.9);
            transform: scale(1.05);
          }
          
          button:focus {
            outline: none;
            ring: 2px solid rgba(255, 255, 255, 0.5);
          }
          
          a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: color 0.2s;
          }
          
          a:hover {
            color: white;
            text-decoration: underline;
          }
        </style>
      </head>
      <body class="gradient-bg">
        <div class="glass-effect">
          <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-white mb-2">Welcome Back</h1>
            <p class="text-white-80">Please sign in to your account</p>
          </div>
          
          <form action="/v1/simulated/login/captcha" method="POST" class="space-y-6">
            <div>
              <label for="email" class="block text-sm font-medium text-white mb-2">
                Email Address
              </label>
              <input
                type="email"
                id="email"
                name="email"
                required
                placeholder="Enter your email"
              />
            </div>
            
            <div>
              <label for="password" class="block text-sm font-medium text-white mb-2">
                Password
              </label>
              <input
                type="password"
                id="password"
                name="password"
                required
                placeholder="Enter your password"
              />
            </div>
            
            <div class="flex items-center justify-between">
              <label class="flex items-center">
                <input type="checkbox" />
                <span class="ml-2 text-sm text-white-80">Remember me</span>
              </label>
              <a href="#" class="text-sm">
                Forgot password?
              </a>
            </div>
            
            <button type="submit">
              Sign In
            </button>
          </form>
          
          <div class="mt-6 text-center">
            <p class="text-white-60 text-sm">
              Don't have an account? 
              <a href="#">Sign up</a>
            </p>
          </div>
        </div>
      </body>
    </html>
  `;
};

export const CaptchaForm = (email: string, error?: string) => {
  return html`
    <!DOCTYPE html>
    <html lang="en">
      <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Security Verification - Kaku</title>
        <style>
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }
          
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
          }
          
          .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
          }
          
          .glass-effect {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
            border-radius: 1rem;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            padding: 2rem;
            width: 100%;
            max-width: 28rem;
          }
          
          .captcha-box {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            text-align: center;
            padding: 20px;
            border-radius: 8px;
            user-select: none;
            margin-bottom: 1rem;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          
          .captcha-box img {
            max-width: 100%;
            max-height: 120px;
            border-radius: 4px;
          }
          
          .captcha-box:hover {
            background: #e9ecef;
            border-color: #adb5bd;
          }
          
          .captcha-gallery-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.2s;
            margin-top: 0.5rem;
          }
          
          .captcha-gallery-btn:hover {
            background: rgba(255, 255, 255, 0.3);
          }
          
          .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
          }
          
          .modal-overlay.active {
            opacity: 1;
            visibility: visible;
          }
          
          .modal-content {
            position: relative;
            max-width: 90vw;
            max-height: 90vh;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          
          .captcha-image {
            max-width: 100%;
            max-height: 80vh;
            border-radius: 0.5rem;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
          }
          
          .modal-controls {
            position: absolute;
            bottom: -60px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 1rem;
            align-items: center;
          }
          
          .control-btn {
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            border: none;
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 18px;
            font-weight: bold;
          }
          
          .control-btn:hover {
            background: white;
            transform: scale(1.1);
          }
          
          .control-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
          }
          
          .control-btn:disabled:hover {
            background: rgba(255, 255, 255, 0.9);
            transform: none;
          }
          
          .image-counter {
            color: white;
            font-size: 0.875rem;
            background: rgba(0, 0, 0, 0.6);
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            backdrop-filter: blur(4px);
          }
          
          .text-center { text-align: center; }
          .mb-8 { margin-bottom: 2rem; }
          .mb-6 { margin-bottom: 1.5rem; }
          .mb-4 { margin-bottom: 1rem; }
          .mb-2 { margin-bottom: 0.5rem; }
          .mt-2 { margin-top: 0.5rem; }
          .mx-auto { margin-left: auto; margin-right: auto; }
          
          .text-3xl { font-size: 1.875rem; }
          .text-sm { font-size: 0.875rem; }
          .text-xs { font-size: 0.75rem; }
          .font-bold { font-weight: 700; }
          .font-semibold { font-weight: 600; }
          .font-medium { font-weight: 500; }
          
          .text-white { color: white; }
          .text-white-80 { color: rgba(255, 255, 255, 0.8); }
          .text-white-60 { color: rgba(255, 255, 255, 0.6); }
          .text-purple-600 { color: #7c3aed; }
          .text-red-200 { color: #fecaca; }
          
          .space-y-6 > * + * { margin-top: 1.5rem; }
          .space-x-4 > * + * { margin-left: 1rem; }
          
          .block { display: block; }
          .flex { display: flex; }
          .items-center { align-items: center; }
          .justify-center { justify-content: center; }
          .w-full { width: 100%; }
          .w-16 { width: 4rem; }
          .h-16 { height: 4rem; }
          .w-8 { width: 2rem; }
          .h-8 { height: 2rem; }
          .flex-1 { flex: 1; }
          
          .bg-white-20 { background: rgba(255, 255, 255, 0.2); }
          .bg-red-500-20 { background: rgba(239, 68, 68, 0.2); }
          .rounded-full { border-radius: 9999px; }
          .rounded-lg { border-radius: 0.5rem; }
          .border-red-500-30 { border: 1px solid rgba(239, 68, 68, 0.3); }
          .p-4 { padding: 1rem; }
          
          input[type="text"], input[type="hidden"] {
            width: 100%;
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            font-size: 1rem;
            transition: all 0.2s;
          }
          
          input[type="text"]::placeholder {
            color: rgba(255, 255, 255, 0.6);
          }
          
          input[type="text"]:focus {
            outline: none;
            ring: 2px solid rgba(255, 255, 255, 0.5);
            border-color: transparent;
          }
          
          button {
            font-weight: 600;
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            border: none;
            cursor: pointer;
            transition: all 0.2s;
            transform: scale(1);
          }
          
          .btn-primary {
            background: white;
            color: #7c3aed;
          }
          
          .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
          }
          
          button:hover {
            transform: scale(1.05);
          }
          
          .btn-primary:hover {
            background: rgba(255, 255, 255, 0.9);
          }
          
          .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
          }
          
          button:focus {
            outline: none;
            ring: 2px solid rgba(255, 255, 255, 0.5);
          }
          
          .error-box {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid rgba(239, 68, 68, 0.3);
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1.5rem;
          }
        </style>
      </head>
      <body class="gradient-bg">
        <div class="glass-effect">
          <div class="text-center mb-8">
            <div class="w-16 h-16 bg-white-20 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
              </svg>
            </div>
            <h1 class="text-3xl font-bold text-white mb-2">Security Check</h1>
            <p class="text-white-80">Please verify you're not a robot</p>
            <p class="text-white-60 text-sm mt-2">Logged in as: ${email}</p>
          </div>
          
          ${error ? html`
            <div class="error-box">
              <p class="text-red-200 text-sm">${error}</p>
            </div>
          ` : ''}
          
          <form action="/v1/simulated/login/success" method="POST" class="space-y-6">
            <input type="hidden" name="email" value="${email}" />
            
            <div>
              <label class="block text-sm font-medium text-white mb-4">
                Please type the word you see below:
              </label>
              <div class="captcha-box" onclick="openGallery()">
                <img src="/captchas/captcha_0.png" alt="Captcha Challenge" />
              </div>
              <button type="button" class="captcha-gallery-btn" onclick="openGallery()">
                📷 View More Captcha Examples
              </button>
              <input
                type="text"
                name="captcha"
                required
                placeholder="Type the word above"
                autocomplete="off"
              />
              <p class="text-white-60 text-xs mt-2">Hint: Any text will work for this demo</p>
            </div>
            
            <div class="flex space-x-4">
              <button
                type="button"
                onclick="window.location.href='/v1/simulated/login'"
                class="flex-1 btn-secondary"
              >
                Back
              </button>
              <button
                type="submit"
                class="flex-1 btn-primary"
              >
                Verify
              </button>
            </div>
          </form>
        </div>
        
        <!-- Captcha Gallery Modal -->
        <div id="captchaModal" class="modal-overlay">
          <div class="modal-content">
            <img id="captchaImage" class="captcha-image" src="" alt="Captcha" />
            <div class="modal-controls">
              <button class="control-btn" onclick="previousImage()" id="prevBtn">‹</button>
              <div class="image-counter">
                <span id="currentIndex">1</span> / <span id="totalImages">8</span>
              </div>
              <button class="control-btn" onclick="nextImage()" id="nextBtn">›</button>
              <button class="control-btn" onclick="closeGallery()" style="margin-left: 1rem;">✕</button>
            </div>
          </div>
        </div>
        
        <script>
          const captchaImages = [
            '/captchas/captcha_0.png',
            '/captchas/captcha_1.png',
            '/captchas/captcha_2.png',
            '/captchas/captcha_3.png',
            '/captchas/captcha_4.png',
            '/captchas/captcha_5.png',
            '/captchas/captcha_6.png',
            '/captchas/captcha_7.png'
          ];
          
          let currentImageIndex = 0;
          
          function openGallery() {
            const modal = document.getElementById('captchaModal');
            modal.classList.add('active');
            showImage(currentImageIndex);
            document.body.style.overflow = 'hidden';
          }
          
          function closeGallery() {
            const modal = document.getElementById('captchaModal');
            modal.classList.remove('active');
            document.body.style.overflow = 'auto';
          }
          
          function showImage(index) {
            const image = document.getElementById('captchaImage');
            const currentIndexSpan = document.getElementById('currentIndex');
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            
            image.src = captchaImages[index];
            currentIndexSpan.textContent = index + 1;
            
            // Update button states
            prevBtn.disabled = index === 0;
            nextBtn.disabled = index === captchaImages.length - 1;
          }
          
          function previousImage() {
            if (currentImageIndex > 0) {
              currentImageIndex--;
              showImage(currentImageIndex);
            }
          }
          
          function nextImage() {
            if (currentImageIndex < captchaImages.length - 1) {
              currentImageIndex++;
              showImage(currentImageIndex);
            }
          }
          
          // Close modal when clicking outside the image
          document.getElementById('captchaModal').addEventListener('click', function(e) {
            if (e.target === this) {
              closeGallery();
            }
          });
          
          // Keyboard navigation
          document.addEventListener('keydown', function(e) {
            const modal = document.getElementById('captchaModal');
            if (modal.classList.contains('active')) {
              switch(e.key) {
                case 'Escape':
                  closeGallery();
                  break;
                case 'ArrowLeft':
                  previousImage();
                  break;
                case 'ArrowRight':
                  nextImage();
                  break;
              }
            }
          });
        </script>
      </body>
    </html>
  `;
};

export const SuccessPage = (email: string) => {
  return html`
    <!DOCTYPE html>
    <html lang="en">
      <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Login Successful - Kaku</title>
        <style>
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }
          
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
          }
          
          .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
          }
          
          .glass-effect {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
            border-radius: 1rem;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            padding: 2rem;
            width: 100%;
            max-width: 28rem;
            text-align: center;
          }
          
          .success-animation {
            animation: successPulse 2s ease-in-out infinite;
          }
          
          @keyframes successPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
          }
          
          .text-center { text-align: center; }
          .mb-8 { margin-bottom: 2rem; }
          .mb-6 { margin-bottom: 1.5rem; }
          .mb-4 { margin-bottom: 1rem; }
          .mb-2 { margin-bottom: 0.5rem; }
          .mt-6 { margin-top: 1.5rem; }
          .mx-auto { margin-left: auto; margin-right: auto; }
          
          .text-3xl { font-size: 1.875rem; }
          .text-sm { font-size: 0.875rem; }
          .text-xs { font-size: 0.75rem; }
          .font-bold { font-weight: 700; }
          .font-semibold { font-weight: 600; }
          
          .text-white { color: white; }
          .text-white-80 { color: rgba(255, 255, 255, 0.8); }
          .text-white-60 { color: rgba(255, 255, 255, 0.6); }
          .text-white-70 { color: rgba(255, 255, 255, 0.7); }
          .text-purple-600 { color: #7c3aed; }
          
          .space-y-4 > * + * { margin-top: 1rem; }
          
          .w-full { width: 100%; }
          .w-20 { width: 5rem; }
          .h-20 { height: 5rem; }
          .w-10 { width: 2.5rem; }
          .h-10 { height: 2.5rem; }
          
          .bg-green-500 { background: #10b981; }
          .bg-white-20 { background: rgba(255, 255, 255, 0.2); }
          .rounded-full { border-radius: 9999px; }
          .rounded-lg { border-radius: 0.5rem; }
          .p-4 { padding: 1rem; }
          
          .flex { display: flex; }
          .items-center { align-items: center; }
          .justify-center { justify-content: center; }
          
          button {
            width: 100%;
            background: white;
            color: #7c3aed;
            font-weight: 600;
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            border: none;
            cursor: pointer;
            transition: all 0.2s;
            transform: scale(1);
          }
          
          button:hover {
            background: rgba(255, 255, 255, 0.9);
            transform: scale(1.05);
          }
          
          button:focus {
            outline: none;
            ring: 2px solid rgba(255, 255, 255, 0.5);
          }
        </style>
      </head>
      <body class="gradient-bg">
        <div class="glass-effect">
          <div class="success-animation w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
          
          <h1 class="text-3xl font-bold text-white mb-4">Welcome!</h1>
          <p class="text-white-80 mb-2">You have successfully logged in</p>
          <p class="text-white-60 text-sm mb-8">Email: ${email}</p>
          
          <div class="space-y-4">
            <div class="bg-white-20 rounded-lg p-4">
              <h3 class="text-white font-semibold mb-2">Login Flow Completed</h3>
              <p class="text-white-70 text-sm">
                ✅ Step 1: Login form submitted<br>
                ✅ Step 2: Captcha verified<br>
                ✅ Step 3: Authentication successful
              </p>
            </div>
            
            <button onclick="window.location.href='/v1/simulated/login'">
              Start Over
            </button>
          </div>
          
          <div class="mt-6 text-center">
            <p class="text-white-60 text-xs">
              This is a demo login flow with simulated captcha verification
            </p>
          </div>
        </div>
      </body>
    </html>
  `;
}; 