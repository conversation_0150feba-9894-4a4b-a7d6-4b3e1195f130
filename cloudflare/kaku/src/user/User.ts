import { z } from 'zod';

export const UserRole = z.enum(['USER', 'SYSTEM', 'INTEGRATION']);

export const UserStatus = z.enum([
  'ACTIVE',
  'INACTIVE',
  'SUSPENDED',
  'UNVERIFIED',
  'DELETED',
  'SOFT_DELETED',
]);

export const USLocale = z.enum(['en_US', 'es_US', 'pt_US']);
export const BRLocale = z.enum(['pt_BR', 'en_BR', 'es_BR']);

export const CountryLocale = z.object({
  type: z.enum(['US', 'BR']),
  locale: z.union([USLocale, BRLocale]),
});

export const UserRiskFlag = z.enum([
  'NORMAL',
  'SUSPICIOUS',
  'HIGH_RECONCILIATION',
  'MULTIPLE_ACCOUNTS',
]);

export const Address = z.object({
  fullAddress: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  region: z.string().optional(),
  postCode: z.string(),
  countryCode: z.string().optional(),
  district: z.string().optional(),
  place: z.string().optional(),
  locality: z.string().optional(),
  neighborhood: z.string().optional(),
  regionCode: z.string().optional(),
  street: z.string().optional(),
  coordinates: z
    .object({
      latitude: z.number(),
      longitude: z.number(),
    })
    .optional(),
});

export const Points = z.object({
  dataPoints: z.number(),
  reputationPoints: z.number(),
  rewardPoints: z.number(),
});

export const ReferralCode = z.object({ value: z.string() });

export const LPOAStatus = z.discriminatedUnion('status', [
  z.object({ status: z.literal('GRANTED'), value: z.string() }), // ISO 8601 date string
  z.object({ status: z.literal('NOT_GRANTED') }),
]);

export const DataUploadStatus = z.enum(['NOT_UPLOADED', 'UPLOADED']);

export const PayoutType = z.enum(['PAYPAL']);

export const UserPayoutMethod = z.object({
  type: PayoutType,
  externalId: z.string().optional(),
});

export const PaymentMethodType = z.enum(['PAYPAL']);

export const PaymentMethod = z.object({
  type: PaymentMethodType,
  currency: z.string(),
  email: z.string(),
  locality: z.string(),
  zip: z.string(),
  country: z.string(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

export const BillingCycle = z.enum(['MONTHLY']);

export const SubscriptionStatus = z.enum(['ACTIVE', 'INACTIVE', 'EXPIRED', 'CANCELLED']);

export const SubscriptionPaymentType = z.enum(['REWARD_POINTS', 'EXTERNAL_PAYMENT']);

export const QuickBucksProSubscription = z.object({
  subscriptionId: z.string(),
  billingCycle: BillingCycle,
  startedAt: z.string(),
  expiresAt: z.string(),
  status: SubscriptionStatus,
  paymentType: SubscriptionPaymentType,
  isAutoRenewalEnabled: z.boolean(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

export const UserSubscriptions = z.object({
  quickBucksProSubscription: QuickBucksProSubscription.optional(),
});

export const NotificationOptions = z.enum(['ENABLED', 'DISABLED', 'DAILY', 'WEEKLY', 'MONTHLY']);

export const EmailNotifications = z.object({
  productNews: NotificationOptions,
  taskUpdates: NotificationOptions,
  recommendedTasks: NotificationOptions,
});

export const UserNotifications = z.object({
  emailNotifications: EmailNotifications,
});

export const UserVerificationStatus = z.discriminatedUnion('status', [
  z.object({ status: z.literal('UNVERIFIED') }),
  z.object({ status: z.literal('INITIATED'), verificationSessionId: z.string() }),
  z.object({ status: z.literal('PROCESSING'), verificationSessionId: z.string() }),
  z.object({
    status: z.literal('VERIFIED'),
    verificationSessionId: z.string(),
    verificationReportId: z.string(),
  }),
  z.object({
    status: z.literal('INPUT_REQUIRED'),
    verificationSessionId: z.string(),
    verificationReportId: z.string().optional(),
    retryEnabled: z.boolean().optional(),
  }),
  z.object({ status: z.literal('REDACTED') }),
  z.object({ status: z.literal('DUPLICATE_VERIFICATION') }),
]);

export const UserSchema = z.object({
  userId: z.string(),
  phone: z.string(),
  username: z.string(),
  status: UserStatus,
  email: z.string(),
  name: z.string().optional(),
  address: Address.optional(),
  avatarUrl: z.string(),
  discordUserId: z.string().optional(),
  points: Points,
  isDemographicSurveyComplete: z.boolean(),
  referralCode: ReferralCode,
  inviterUserReferralCode: ReferralCode,
  lpoaStatus: LPOAStatus,
  dataUploadStatus: DataUploadStatus,
  paymentMethod: PaymentMethod.optional(),
  payoutMethods: z.array(UserPayoutMethod).optional(),
  role: UserRole,
  poolConnections: z.number(),
  activeAgents: z.number(),
  isDeleted: z.boolean(),
  tierId: z.string(),
  countryLocale: CountryLocale,
  hasWithdrawnInitialPayout: z.boolean(),
  userSubscriptions: UserSubscriptions,
  notifications: UserNotifications,
  riskFlags: z.array(UserRiskFlag),
  verificationItemId: z.string().optional(),
  verificationStatus: UserVerificationStatus,
  createdAt: z.string(),
  updatedAt: z.string(),
});

export type User = z.infer<typeof UserSchema>;
