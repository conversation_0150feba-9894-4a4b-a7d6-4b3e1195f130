import { GoogleGenAI } from '@google/genai';
import {
  generateCacheKeyForStateChangeLLMCalls,
  generateCacheKeyFromScreenshot,
} from '../common/utils';
import { LLMRepository } from './LLMRepository';
import { DetectPageStateChangeLlmRequest } from './types/detect-page-state-change-llm-request';
import { LLMRequest } from './types/llm-request';
import { LLMResponse } from './types/llm-response';

export class GeminiLLMRepository implements LLMRepository {
  private readonly apiKey: string;
  private readonly baseURL: string;

  constructor(apiKey: string, baseURL: string) {
    this.apiKey = apiKey;
    this.baseURL = baseURL;
  }
  async getLLMResponse(llmRequest: LLMRequest): Promise<LLMResponse> {
    const start = Date.now();

    const cacheKey = generateCacheKeyFromScreenshot(llmRequest, llmRequest.version);

    const ai = new GoogleGenAI({
      apiKey: this.apiKey,
      httpOptions: {
        baseUrl: this.baseURL,
        headers: {
          'cf-aig-cache-key': cacheKey,
          'cf-aig-skip-cache': llmRequest.skipCache.toString(),
          'cf-aig-metadata': JSON.stringify({
            linkId: llmRequest.linkId
          }),
        },
      },
    });

    const response = await ai.models.generateContent({
      model: 'gemini-2.5-flash-lite',
      contents: [
        {
          inlineData: {
            mimeType: 'image/webp',
            data: llmRequest.screenshot,
          },
        },
      ],

      config: {
        topP: 1.0,
        topK: 1,
        temperature: 0.0,
        maxOutputTokens: 32768,
        systemInstruction: llmRequest.prompt,
        responseMimeType: 'application/json',
        responseSchema: llmRequest.responseSchema,
        seed: 100,
        thinkingConfig: {
          includeThoughts: false,
        },
      },
    });

    const end = Date.now();

    if (!response.text) {
      throw new Error('Invalid response from Gemini API');
    }

    return {
      output_text: response.text,
      callDuration: end - start,
    };
  }

  async detectStateChangeFromPreviousFormVisionResult(
    detectPageStateChangeLlmRequest: DetectPageStateChangeLlmRequest,
  ): Promise<LLMResponse> {
    const start = Date.now();
    const cacheKey = generateCacheKeyForStateChangeLLMCalls(detectPageStateChangeLlmRequest);

    const ai = new GoogleGenAI({
      apiKey: this.apiKey,
      httpOptions: {
        baseUrl: this.baseURL,
        headers: {
          'cf-aig-cache-key': cacheKey,
          'cf-aig-skip-cache': detectPageStateChangeLlmRequest.skipCache.toString(),
          'cf-aig-metadata': JSON.stringify({
            linkId: detectPageStateChangeLlmRequest.linkId
          }),
        },
      },
    });

    const response = await ai.models.generateContent({
      model: 'gemini-2.5-flash-lite-preview-06-17',
      contents: [
        {
          inlineData: {
            mimeType: 'image/webp',

            data: detectPageStateChangeLlmRequest.screenshot,
          },
        },
      ],

      config: {
        topP: 1.0,
        topK: 1,
        temperature: 0.0,
        maxOutputTokens: 32768,
        systemInstruction:
          detectPageStateChangeLlmRequest.prompt +
          JSON.stringify(detectPageStateChangeLlmRequest.agentVisionResultState),
        seed: 100,
        thinkingConfig: {
          includeThoughts: false,
        },
      },
    });

    const end = Date.now();

    if (!response.text) {
      throw new Error('Invalid response from Gemini API');
    }

    return {
      output_text: response.text,
      callDuration: end - start,
    };
  }
}
