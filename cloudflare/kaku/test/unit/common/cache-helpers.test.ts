import { describe, it, expect } from 'vitest';
import {
  hashInfo,
  generateCacheKey,
  generateCacheKeyFromScreenshot,
} from '../../../src/common/utils/cache-helpers';
import { LLMRequest } from '../../../src/llm/types/llm-request';

describe('Cache Helpers', () => {
  describe('hashInfo', () => {
    it('should generate consistent 8-character hash for same input', () => {
      const testImage =
        'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';

      const hash1 = hashInfo(testImage);
      const hash2 = hashInfo(testImage);

      expect(hash1).toBe(hash2);
      expect(hash1).toHaveLength(8);
      expect(hash1).toMatch(/^[a-f0-9]{8}$/);
    });

    it('should generate different hashes for different inputs', () => {
      const image1 = 'image1';
      const image2 = 'image2';

      const hash1 = hashInfo(image1);
      const hash2 = hashInfo(image2);

      expect(hash1).not.toBe(hash2);
    });
  });

  describe('generateCacheKey', () => {
    it('should generate cache key in correct format', () => {
      const cacheKey = generateCacheKey('github', 'a1b2c3d4', 1024, 768, 'v1', 'prompt');

      expect(cacheKey).toBe('github_a1b2c3d4_prompt_1024x768_v1');
    });

    it('should handle different platforms and versions', () => {
      const cacheKey = generateCacheKey('facebook', '12345678', 800, 600, 'v2', 'prompt');

      expect(cacheKey).toBe('facebook_12345678_prompt_800x600_v2');
    });
  });

  describe('generateCacheKeyFromScreenshot', () => {
    it('should generate cache key from screenshot with default version', () => {
      const testImage =
        'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';

      const cacheKey = generateCacheKeyFromScreenshot({
        platform: 'github',
        screenshot: testImage,
        viewportWidth: 1024,
        viewportHeight: 768,
        prompt: 'prompt',
        skipCache: false,
      });

      // The function uses default version 'v1.0.0' when no version is provided
      const expectedPattern = new RegExp(`^github_[a-f0-9]{8}_[a-f0-9]{8}_1024x768_v1\\.0\\.0$`);

      expect(cacheKey).toMatch(expectedPattern);
    });

    it('should generate cache key from screenshot with custom version', () => {
      const testImage =
        'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';

      const cacheKey = generateCacheKeyFromScreenshot(
        {
          platform: 'github',
          screenshot: testImage,
          viewportWidth: 1024,
          viewportHeight: 768,
          prompt: 'prompt',
          skipCache: false,
        },
        'v1.0.3',
      );

      const expectedPattern = new RegExp(`^github_[a-f0-9]{8}_[a-f0-9]{8}_1024x768_v1\\.0\\.3$`);

      expect(cacheKey).toMatch(expectedPattern);
    });

    it('should generate consistent keys for same inputs', () => {
      const testImage = 'test-image';

      const key1 = generateCacheKeyFromScreenshot({
        platform: 'github',
        screenshot: testImage,
        viewportWidth: 1024,
        viewportHeight: 768,
        prompt: 'prompt',
        skipCache: false,
      });

      const key2 = generateCacheKeyFromScreenshot({
        platform: 'github',
        screenshot: testImage,
        viewportWidth: 1024,
        viewportHeight: 768,
        prompt: 'prompt',
        skipCache: false,
      });

      expect(key1).toBe(key2);
    });

    it('should handle different platforms', () => {
      const testImage = 'test-image';

      const llmRequest: LLMRequest = {
        platform: 'facebook',
        prompt: 'test prompt',
        screenshot: testImage,
        skipCache: false,
        viewportWidth: 1024,
        viewportHeight: 768,
      };

      const cacheKey = generateCacheKeyFromScreenshot(llmRequest, 'v2.0.1');

      // The pattern should include both image hash and prompt hash
      expect(cacheKey).toMatch(/^facebook_[a-f0-9]{8}_[a-f0-9]{8}_1024x768_v2\.0\.1$/);
    });
  });
});
