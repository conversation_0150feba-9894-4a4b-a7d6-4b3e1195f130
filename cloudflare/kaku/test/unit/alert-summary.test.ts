/**
 * Test for alert summary component
 */

import { describe, it, expect } from 'vitest';
import { generateAlertSummary } from '../../src/form-generation/components/alert-summary';

describe('Alert Summary Component', () => {
  it('should return empty string when alertsSummary is empty', () => {
    const result = generateAlertSummary('');
    expect(result).toBe('');
  });

  it('should generate alert summary with beautiful error styling', () => {
    const result = generateAlertSummary('Your login credentials are incorrect');

    expect(result).toContain('Your login credentials are incorrect');
    expect(result).toContain('bg-gradient-to-r from-red-50 to-red-100'); // Beautiful gradient background
    expect(result).toContain('text-red-600'); // Error icon color
    expect(result).toContain('text-red-800'); // Text color
    expect(result).toContain('rounded-xl'); // Rounded corners
  });

  it('should generate alert summary for longer messages', () => {
    const longMessage = 'Multiple issues detected with your account that need immediate attention';
    const result = generateAlertSummary(longMessage);

    expect(result).toContain(longMessage);
    expect(result).toContain('bg-gradient-to-r from-red-50 to-red-100'); // Beautiful gradient background
    expect(result).toContain('relative mb-6 rounded-xl'); // Container styling
  });

  it('should include error icon in the alert', () => {
    const result = generateAlertSummary('Test alert message');

    expect(result).toContain('<svg');
    expect(result).toContain('text-red-600');
    expect(result).toContain('viewBox="0 0 20 20"');
  });
});
