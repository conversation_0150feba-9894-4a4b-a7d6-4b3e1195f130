import { ErrorCode, localizationMessages, SupportedLocales } from './localizationMessages';

interface ErrorDetail {
  errorCode: string;
  message: string;
}

interface ErrorResponseParams {
  path: string;
  method: string;
  detail: ErrorDetail[];
  statusCode?: number;
  type?: string;
  title?: string;
}

export function createErrorDetail(
  errorCode: ErrorCode,
  lang: SupportedLocales = 'en',
  placeholders?: Record<string, string>,
): ErrorDetail {
  let message = localizationMessages[lang][errorCode];
  if (placeholders) {
    for (const [key, value] of Object.entries(placeholders)) {
      message = message.replace(`{${key}}`, value);
    }
  }

  return { errorCode, message };
}

export function formatErrorResponse({
  path,
  method,
  detail,
  statusCode = 400,
  type = '',
  title = '',
}: ErrorResponseParams) {
  return {
    timestamp: new Date().toISOString(),
    path,
    method,
    type,
    status: statusCode,
    title,
    instance: path,
    detail,
  };
}
