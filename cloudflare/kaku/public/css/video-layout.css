/* Video container styles */
#video-container {
  border-radius: 1rem;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 2px solid #e5e7eb;
  padding: 20px;
  background: #ffffff;
  position: relative;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 20px;
  box-sizing: border-box;
}

#video-container::before {
  display: none;
}

#video-container:hover:not(.input-overlay-active) {
  transform: none;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

#video-container.input-overlay-active {
  transform: none !important;
}

#video-header {
  background: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  border-radius: 1rem 1rem 0 0;
  position: relative;
  padding: 1.5rem 1rem 1rem 1rem;
}

#video-header::before {
  display: none;
}

#video-header h2 {
  color: #374151;
  font-weight: 600;
  font-size: 1.125rem;
  margin: 0;
  text-align: left;
}

#video-header p {
  color: #6b7280;
  font-weight: 400;
  font-size: 0.875rem;
  margin: 0.25rem 0 0 0;
  text-align: left;
}

#interactivity-overlay {
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
}

#remoteVideo {
  object-fit: contain !important;
  border-radius: 0 0 1rem 1rem;
  transition: all 0.3s ease;
  position: relative;
}

#remoteVideo::before {
  display: none;
}

/* Faded blur border effects - 20px border area with gradient blur intensity */
.blur-border-edges {
  position: absolute;
  z-index: 15;
  pointer-events: none;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.3s ease-in-out;
}

.blur-border-top {
  top: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(255, 255, 255, 0.6) 25%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0.2) 75%,
    transparent 100%
  );
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

.blur-border-bottom {
  bottom: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(
    to top,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(255, 255, 255, 0.6) 25%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0.2) 75%,
    transparent 100%
  );
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

.blur-border-left {
  top: 0;
  bottom: 0;
  left: 0;
  width: 20px;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(255, 255, 255, 0.6) 25%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0.2) 75%,
    transparent 100%
  );
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

.blur-border-right {
  top: 0;
  bottom: 0;
  right: 0;
  width: 20px;
  background: linear-gradient(
    to left,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(255, 255, 255, 0.6) 25%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0.2) 75%,
    transparent 100%
  );
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

/* Corner blur effects for the 20px border area */
.blur-border-corner {
  position: absolute;
  z-index: 20;
  pointer-events: none;
  width: 20px;
  height: 20px;
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
}

.blur-border-corner-tl {
  top: 0;
  left: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%
  );
}

.blur-border-corner-tr {
  top: 0;
  right: 0;
  background: linear-gradient(
    225deg,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%
  );
}

.blur-border-corner-bl {
  bottom: 0;
  left: 0;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%
  );
}

.blur-border-corner-br {
  bottom: 0;
  right: 0;
  background: linear-gradient(
    315deg,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%
  );
}

/* Hide unused blur effects */
.blur-border-unified,
.blur-border-shadow {
  display: none;
}

.mobile-fullscreen {
  width: 100vw !important;
  max-width: 100vw !important;
  height: 100vh !important;
  max-height: 100vh !important;
  border-radius: 0 !important;
  margin: 0 !important;
}

.loading-indicator {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.loading-indicator::before {
  display: none;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  #video-container {
    border-radius: 0;
    border: none;
    box-shadow: none;
    background: #ffffff;
  }

  #video-header {
    padding: 1rem;
    border-radius: 0;
    background: #ffffff;
    border-bottom: 1px solid #e5e7eb;
  }

  #video-header h2 {
    font-size: 1rem;
    color: #374151;
  }

  #video-header p {
    font-size: 0.875rem;
    color: #6b7280;
  }

  #remoteVideo {
    border-radius: 0;
  }

  .blur-border-edges,
  .blur-border-corner {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }

  .blur-border-top,
  .blur-border-bottom,
  .blur-border-left,
  .blur-border-right {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }
}
