export enum ConnectionWorkflowState {
  OnCaptcha = 'on_captcha',
  UserFormFilled = 'user_form_filled',
  Authenticated = 'authenticated',
  CaptchaResolved = 'capture_resolved',
}

export async function storeConnectionScreenshotToR2(
  bucket: R2Bucket,
  userId: string,
  platformId: string,
  sessionId: string,
  connectionWorkflowState: ConnectionWorkflowState,
  base64Image: string,
): Promise<void> {
  const image = base64ToBytes(base64Image);
  const path = `base64_screenshots/${userId}/${platformId}/${sessionId}_${connectionWorkflowState}_${Date.now().toString()}.png`;

  await bucket.put(path, image);
}

function base64ToBytes(input: string): Uint8Array {
  //Remove base64 metadata
  const base64 = input.includes(',') ? input.split(',')[1] : input;
  const binaryString = atob(base64);
  const len = binaryString.length;
  const bytes = new Uint8Array(len);
  for (let i = 0; i < len; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  return bytes;
}
