# Kaku - Connection Agent

## Overview

Kaku is a Cloudflare Agent-powered service that enables users to connect to various third-party services (Facebook, Gmail, Amazon, Spotify, Netflix, etc.) through a dynamic, AI-generated interface. The system discovers login flows on-the-fly and manages the authentication process, eliminating the need for predefined integration knowledge.

## Architecture

![img.png](kaku-architecture-diagram.png)

```mermaid
graph TD
    User[User] <--> |HTMX/WebSockets| Connection[Connection Agent]
    Connection <--> |Browser Context| Browserbase[Browserbase]
    Browserbase <--> |Page Interaction| Services[External Services]
    Connection <--> |AI Prompts| LLM[LLM via Agents AI SDK]
    LLM --> |HTMX Generation| Connection

    subgraph "Worker (Hono)"
        Connection
        LLM
    end

    subgraph "Vercel"
        Browserbase
    end

    subgraph "Third Party"
        Services
    end
```

### Key Components

#### 1. Connection Agent (Cloudflare Agents)

- Stateful service built on Cloudflare's Agent SDK and Durable Objects
- Manages authentication state and service-specific logic
- Each service gets its own dedicated agent instance (e.g., `api/connections/facebook`)
- Generates dynamic HTMX interfaces using LLM-powered code generation

#### 2. Browserbase Integration

- Remote browser service running on Vercel
- Handles actual browser interactions with third-party services
- Provides page context (forms, fields, buttons) back to the Connection Agent
- Executes field filling and submission based on user input

#### 3. LLM Integration

- Uses Cloudflare Agents AI SDK for LLM access
- Transforms browser context into user-friendly HTMX interfaces
- Adapts to different service requirements dynamically
- No predefined knowledge base required - works with just service URL

#### 4. HTMX Frontend

- Lightweight, JavaScript-free interactive interfaces
- Real-time updates via WebSockets
- Dynamic form generation specific to each service
- Seamless user experience with minimal client-side code

### Data Flow

1. User requests connection to a service (e.g., Facebook)
2. Connection Agent initializes and sends the login URL to Browserbase
3. Browserbase loads the page and returns context (form fields, layout)
4. LLM processes this context and generates HTMX interface
5. User interacts with the HTMX interface, providing credentials
6. Connection Agent forwards credentials to Browserbase
7. Browserbase completes the authentication flow
8. Connection status and results are streamed back to the user

## Agent URL Mapping

Our Hono app uses a middleware called `agentsMiddleware` to map agent class names to URL routes automatically. Here's how it works:

The `agentsMiddleware` function:

1. Detects whether the incoming request is a WebSocket connection or standard HTTP request
2. Routes the request to the appropriate agent
3. Handles WebSocket upgrades for persistent connections

![img_1.png](agents-middleware-architecture.png)

## Adding Puppeteer Client Scripts

To inject client-side scripts via Puppeteer:
Create a file in `src/client/`, e.g. `my-script.mjs`.

Update Rollup config:
`standardConfig('src/client/my-script.mjs', 'my-script')`

Build:
npm run build-client

Inject in Puppeteer:

```js
const res = await fetch(`${this.env.KAKU_API_ENDPOINT}/out/my-script.min.js`);
const script = await res.text();

await this.kazeelClient.send('Page.addScriptToEvaluateOnNewDocument', {
  source: script,
});
```

### URL Transformation Rules

1. **Class to URL Transformation**:

   - Agent class names are transformed to kebab-case in URLs
   - Capital letters become lowercase and are preceded by hyphens
   - Example: `ConnectionAgent` → `/api/connection-agent/[instanceId]`
   - In our implementation: `Connections` → `/api/connections/[instanceId]`

2. **URL Anatomy**:
   - `/api` - Default prefix (configurable via middleware options)
   - `/connections` - Kebab-cased agent class name
   - `/[instanceId]` - Unique identifier for a specific agent instance (e.g., service name)

The `agentsMiddleware` handles HTTP and WebSocket connections to these endpoints, routing them to the appropriate agent instances based on the URL path.

## Development

### Prerequisites

- Node.js 18+
- Wrangler CLI

### Setup

```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

### Testing

```bash
npm test
```
