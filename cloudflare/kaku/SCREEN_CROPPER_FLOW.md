# Screen Cropper Initialization and WebRTC Flow Documentation

## Overview

This document describes the complete flow of script injection, WebSocket/WebRTC initialization, and screen cropper streaming in the Kaku system.

## Flow Sequence

### 1. Script Injection to Isolated World

**Location**: `src/workflow/connections-workflow.ts` → `injectScriptsIntoAnIsolatedWorld()`

**Process**:

1. Creates isolated world context with `Page.createIsolatedWorld`
2. Injects scripts in sequence:
   - TensorFlow.js library
   - Screenshot comparison script
   - Browser controller script
   - **Initialize browser controller** (with browserWsEndpoint)
   - Screen cropper script
   - **Initialize screen cropper** (with WebSocket endpoint + viewport)
   - Captcha detector script
   - TensorFlow captcha detector script
   - TF model bundle script
   - **Initialize TensorFlow detector** (with viewport config)

### 2. Screen Cropper Initialization

**Location**: `src/client/screen-cropper.ts` → `init(wsEndpoint, viewPort)`

**Process**:

```javascript
async function init(wsEndpoint, viewPort) {
  // Step 1: Wait for browser controller availability
  await waitFor<PERSON>rowser<PERSON>ontroller();

  // Step 2: Setup WebSocket connection
  await connectToSocket(wsEndpoint);

  // Step 3: Setup WebRTC connection with input channel
  await connectToWebRTC();

  isInitialized = true;
}
```

#### 2.1 WebSocket Connection Setup

**Function**: `connectToSocket(wsEndpoint)`

- Creates WebSocket connection
- Waits for 'open' event
- Sets `isSocketReady = true`
- Processes any pending socket operations
- Sets up message handlers via `setupSocketMessageHandlers()`

#### 2.2 WebRTC Connection Setup

**Function**: `connectToWebRTC()`

- Creates RTCPeerConnection with ICE servers
- Sets up ICE candidate handling
- Monitors connection state changes
- Creates input data channel immediately
- Sets `isWebRTCReady = true` when connected
- Returns promise that resolves when WebRTC is fully connected

### 3. WebSocket Message Handlers

**Function**: `setupSocketMessageHandlers()`

Handles WebRTC signaling:

- `offer`: Sets remote description, creates answer
- `answer`: Sets remote description
- `candidate`: Adds ICE candidate
- `ready`: Creates and sends offer
- `interactivity-status`: Controls frame sending
- `trigger-comparison`: Forwards to captcha detector
- `request-frame`: Delegates to browser controller

### 4. Screen Cropper Start (Streaming)

**Location**: `src/client/screen-cropper.ts` → `start(viewPort)`

**Called from**: Connection workflow via `initRTCSteaming()`

**Process**:

```javascript
async function start(viewPort) {
  // Ensure initialization is complete
  if (!isInitialized) throw new Error('Must be initialized first');

  // Get initial bounding box from TensorFlow detector
  const cropBox = await window.tfCaptchaDetector.getInitialBoundingBox(viewPort);

  // Start actual streaming
  await startStreaming(viewPort, cropBox);
}
```

### 5. Initial Bounding Box and Socket Readiness

**Issue Resolved**: `getInitialBoundingBox()` calls `updateCropBox()` which needs socket

**Solution**: Deferred socket operations

```javascript
function updateCropBox(newCropBox) {
  // Update local crop region
  cropRegion = { ... };

  const sendCropBoxUpdate = () => {
    if (socket?.readyState === WebSocket.OPEN) {
      socket.send(JSON.stringify({
        type: 'cropbox-update',
        cropBox: newCropBox,
      }));
    }
  };

  if (isSocketReady) {
    sendCropBoxUpdate();
  } else {
    // Queue for when socket becomes ready
    pendingSocketOperations.push(sendCropBoxUpdate);
  }
}
```

### 6. Input Channel Creation and WebRTC Signaling

**Issue Resolved**: "Input channel not ready" errors due to improper timing

**Root Cause**: Input channel was created during `init()` but UI side wasn't ready to receive it

**Solution**: Create input channel during WebRTC signaling

```javascript
case 'ready':
  // Create input channel before creating offer so it's included in the offer
  log('🔧 [ready] Creating input channel before offer...');
  createInputChannel();

  const offer = await pc.createOffer();
  await pc.setLocalDescription(offer);
  socket.send(JSON.stringify({ type: 'offer', offer }));
  log('✅ [ready] Offer sent with input channel');
  break;
```

**Flow**:

1. UI sends `ready` message when WebRTC is initialized
2. Screen cropper creates input channel
3. Screen cropper creates offer (includes input channel)
4. UI receives offer and sets up `ondatachannel` handler
5. Input channel becomes available on UI side via `pc.ondatachannel`

### 7. Streaming Process

**Function**: `startStreaming(viewPort, cropBox)`

1. Gets display media stream via `getDisplayMedia()`
2. Sets up browser metrics via browser controller
3. Creates MediaStreamTrackProcessor and MediaStreamTrackGenerator
4. Sets up transform stream for frame processing
5. Adds cropped track to peer connection
6. Sends interactivity status via socket

## Key Improvements

### 1. Proper Initialization Sequence

- **Before**: Socket created during streaming, causing race conditions
- **After**: Socket and WebRTC established during init phase

### 2. WebRTC Handshake Protection

- Connection state monitoring ensures handshake completion
- Input channel created early and ready for use

### 3. Socket Readiness Handling

- Pending operations queue for early `updateCropBox` calls
- Automatic processing when socket becomes ready

### 4. Clear Separation of Concerns

- `init()`: Setup connections and channels
- `start()`: Begin actual streaming

## State Management

```javascript
// Initialization state
let isInitialized = false;
let isSocketReady = false;
let isWebRTCReady = false;
let pendingSocketOperations = [];
```

## Error Handling

- Proper error propagation from init phase
- Graceful handling of socket/WebRTC failures
- Timeout protection for connection establishment

## Testing Considerations

1. **Unit Tests**: Test each phase independently
2. **Integration Tests**: Test complete flow end-to-end
3. **Error Scenarios**: Test connection failures and recovery
4. **Race Conditions**: Verify socket readiness handling
