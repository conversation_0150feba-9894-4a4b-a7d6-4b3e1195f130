import { <PERSON><PERSON>Form, CaptchaForm, SuccessPage } from '../ui/components/login-flow';
import { <PERSON><PERSON> } from 'hono';
import { <PERSON>kuApp } from '../common/types';

const app = new Hono<KakuApp>();

// Simulated Login Flow -  Will help us test the login workflow
app.get('/login', (c) => {
  return c.html(LoginForm());
});
app.post('/login/captcha', async (c) => {
  const body = await c.req.parseBody();
  const email = body.email as string;

  return c.html(CaptchaForm(email));
});
app.post('/login/success', async (c) => {
  const body = await c.req.parseBody();
  const email = body.email as string;

  return c.html(SuccessPage(email));
});

export { app as SimulatedLoginHandler };