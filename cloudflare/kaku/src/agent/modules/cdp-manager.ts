import { CDP } from '../../browser/simple-cdp';
import {
  CDPAttachedToTargetParams,
  CDPConsoleAPIParams,
  CDPEvent,
  CDPRuntimeBindingCalledParams,
  CDPRuntimeExceptionParams,
} from '../../browser/types/cdp-events';
import { startCaptchaMonitoring } from '../../browser';
import { ErrorCollector, ErrorContext, ErrorRouter } from '../../common/error';
import { CaptchaSolvedPayload } from '../types/captcha-solved';

/**
 * CDP Manager Module
 * Handles all Chrome DevTools Protocol related functionality
 * Uses function-level callbacks instead of constructor injection
 */
export class CDPManager {
  private cdpErrorHandlers: (() => void) | null = null;
  private config = {
    debug: false,
  };

  private log(...args: any[]): void {
    if (this.config.debug) {
      console.log('[kazeel][cdp-manager]', ...args);
    }
  }

  /**
   * Handle target attachment events from CDP
   */
  async onAttachedToTarget(
    cdpClient: CDP,
    { params }: CDPEvent<CDPAttachedToTargetParams>,
    onTargetAttached: (sessionId: string) => void,
  ): Promise<void> {
    const { sessionId, targetInfo } = params;

    if (targetInfo.type === 'page') {
      // Notify parent class about new session
      onTargetAttached(sessionId);
      await cdpClient.Page.enable(undefined, sessionId);
      await cdpClient.Runtime.enable(undefined, sessionId);
    }
  }

  /**
   * set auto attach
   */
  async setAuthAttach(cdpClient: CDP) {
    await cdpClient.Target.setAutoAttach({
      autoAttach: true,
      flatten: true,
      waitForDebuggerOnStart: false,
    });
  }

  /**
   * Set up CDP error monitoring with runtime exception and console API handlers
   */
  setupCDPErrorMonitoring(
    cdpClient: CDP,
    userContext: {
      userId: string;
      platformId: string;
      referenceId: string;
      sessionId: string;
    },
    onCDPErrorDetected: (error: ErrorContext) => void,
  ): void {
    if (!cdpClient) return;

    const handleRuntimeException = (event: CDPEvent<CDPRuntimeExceptionParams>) => {
      const exceptionDetails = event.params?.exceptionDetails;

      if (!exceptionDetails.text.includes('kazeel')) return;

      const errorContext = ErrorCollector.collectError(
        'cdp',
        'SCRIPT_INJECTION_FAILED',
        event,
        'error',
        userContext,
      );

      const classifiedError = ErrorRouter.classifyError(errorContext);
      onCDPErrorDetected(classifiedError);
    };

    const handleConsoleAPI = (event: CDPEvent<CDPConsoleAPIParams>) => {
      // Extract console API details from event.params (simple-cdp event structure)
      const consoleType = event.params?.type;
      const exceptionDetails = event.params?.exceptionDetails;

      if (consoleType === 'error' && !exceptionDetails?.text?.includes('kazeel')) return;

      if (consoleType === 'error') {
        const errorContext = ErrorCollector.collectError(
          'cdp',
          'SCRIPT_INJECTION_FAILED',
          event,
          'error',
          userContext,
        );

        const classifiedError = ErrorRouter.classifyError(errorContext);
        onCDPErrorDetected(classifiedError);
      }
    };

    // Set up CDP event listeners
    cdpClient.Runtime.addEventListener('exceptionThrown', handleRuntimeException);
    cdpClient.Runtime.addEventListener('consoleAPICalled', handleConsoleAPI);

    this.cdpErrorHandlers = () => {
      if (cdpClient) {
        cdpClient.Runtime.removeEventListener('exceptionThrown', handleRuntimeException);
        cdpClient.Runtime.removeEventListener('consoleAPICalled', handleConsoleAPI);
      }
    };
  }

  /**
   * Clean up CDP error monitoring
   */
  cleanupCDPErrorMonitoring(): void {
    if (this.cdpErrorHandlers) {
      this.cdpErrorHandlers();
      this.cdpErrorHandlers = null;
    }
  }

  /**
   * Set up captcha monitoring with runtime bindings
   */
  async startCaptchaMonitoring(
    cdpClient: CDP,
    targetSessionId: string,
    executionContextId: number,
    onCaptchaSolved: (event: CaptchaSolvedPayload) => void,
  ): Promise<void> {
    if (!cdpClient || !targetSessionId) {
      throw new Error('CDP client or target session not available');
    }

    const bindingListener = async ({ params }: CDPEvent<CDPRuntimeBindingCalledParams>) => {
      if (params.name === '__captchaSolved__') {
        try {
          const payload = JSON.parse(params.payload);
          if (payload.type === 'CAPTCHA_SOLVED') {
            this.log(`Received CAPTCHA_SOLVED via Binding`);
            // Notify parent about captcha solved
            onCaptchaSolved({
              differencePercentage: payload.differencePercentage,
              timestamp: payload.timestamp,
              executionContextId,
              source: payload.source,
            });
          } else {
            console.warn(
              'Received unexpected payload type on __captchaSolved__ binding:',
              payload.type,
            );
          }
        } catch (parseError) {
          console.error(
            'Failed to parse payload from __captchaSolved__ binding:',
            params,
            parseError,
          );
        }
        return;
      }

      console.warn(`Received unhandled binding call: ${JSON.stringify(params)}`);
    };

    try {
      await cdpClient.Runtime.addBinding(
        {
          name: '__captchaSolved__',
          executionContextId,
        },
        targetSessionId,
      );
      this.log('Added Runtime binding for captcha solved notifications.');

      cdpClient.Runtime.removeEventListener('bindingCalled', bindingListener);
      cdpClient.Runtime.addEventListener('bindingCalled', bindingListener);
      this.log('Attached listener for Runtime.bindingCalled');
    } catch (bindingError) {
      console.error('Failed to set up one or more Runtime bindings:', bindingError);
      return;
    }

    await startCaptchaMonitoring(
      cdpClient,
      {
        diffThreshold: 5,
        screenshotQuality: 90,
      },
      executionContextId,
      targetSessionId,
    );
  }
}
