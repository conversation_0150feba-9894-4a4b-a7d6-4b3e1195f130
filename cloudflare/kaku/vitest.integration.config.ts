import { defineWorkersConfig } from '@cloudflare/vitest-pool-workers/config';

export default defineWorkersConfig(async () => {
  return {
    test: {
      include: ['test/integration/**/*.{test,spec}.{js,mjs,cjs,ts,tsx,jsx}'],
      setupFiles: ['./vitest.setup.ts'],
      testTimeout: 30000,
      globals: true,
      deps: {
        optimizer: {
          ssr: {
            include: [
              // vitest can't seem to properly import
              // `require('./path/to/anything.json')` files,
              // which ajv uses (by way of @modelcontextprotocol/sdk)
              // the workaround is to add the package to the include list
              // this issue was introduced with the agents upgrade
              'ajv',
            ],
          },
        },
      },
      poolOptions: {
        workers: {
          singleWorker: true,
          isolatedStorage: false,
          miniflare: {
            compatibilityDate: '2025-04-17',
            compatibilityFlags: ['nodejs_compat_v2'],
            main: 'src/api/index.ts',
            durable_objects: {
              bindings: [
                { name: 'Connections', class_name: 'Connections' },
                { name: 'CoordinatorDO', class_name: 'CoordinatorDO' },
              ],
              migrations: [
                {
                  tag: 'v1',
                  new_sqlite_classes: ['Connections'],
                },
                {
                  tag: 'v2',
                  new_sqlite_classes: ['CoordinatorDO'],
                },
              ],
            },
            r2_buckets: [
              {
                binding: 'SCREENSHOTS_INBOUND_BUCKET',
                bucket_name: 'test-screenshots-bucket',
              },
            ],
          },
          wrangler: { configPath: './wrangler.jsonc' },
        },
      },
    },
  };
});
