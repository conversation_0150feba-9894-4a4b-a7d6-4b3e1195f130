/**
 * Alert summary component
 */

/**
 * Generate beautiful error container for alert summary
 */
export function generateAlertSummary(alertsSummary: string): string {
  if (!alertsSummary) return '';

  return `
    <div class="relative mb-6 rounded-xl bg-gradient-to-r from-red-50 to-red-100 border border-red-200 shadow-sm overflow-hidden">
      <!-- Left accent border -->
      <div class="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-red-400 to-red-600"></div>

      <!-- Content container -->
      <div class="p-4 pl-6">
        <div class="flex items-center space-x-3">
          <!-- Error icon with subtle background -->
          <div class="flex-shrink-0 mt-0.5">
            <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center border border-red-200">
              <svg class="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
              </svg>
            </div>
          </div>

          <!-- Alert message -->
          <div class="flex-1 min-w-0">
            <p class="text-sm font-medium text-red-800 leading-relaxed">
              ${alertsSummary}
            </p>
          </div>
        </div>
      </div>

      <!-- Subtle bottom highlight -->
      <div class="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-red-300 to-transparent opacity-50"></div>
    </div>
  `;
}
