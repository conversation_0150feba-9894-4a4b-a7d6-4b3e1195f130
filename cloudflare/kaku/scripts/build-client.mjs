#!/usr/bin/env node

import { build } from 'vite';
import { resolve } from 'path';
import crypto from 'crypto';
import fs from 'fs';

function generateContentHash(content) {
  return crypto.createHash('md5').update(content).digest('hex').substring(0, 8);
}

const clientFiles = [
  {
    input: 'src/client/browser-controller-proxy.ts',
    name: 'browser-controller-proxy',
    format: 'iife',
    globalName: 'BrowserControllerProxyModule',
  },
  {
    input: 'src/client/screen-cropper.ts',
    name: 'screen-cropper',
    format: 'iife',
    globalName: 'ScreenCropperModule',
  },
  {
    input: 'src/client/captcha-detector.ts',
    name: 'captcha-detector',
    format: 'iife',
    globalName: 'CaptchaDetectorModule',
  },
  {
    input: 'src/client/captcha-detector-tf.mjs',
    name: 'captcha-detector-tf',
    format: 'iife',
    globalName: 'CaptchaDetectorTfModule',
  },
  {
    input: 'src/client/utils/screenshot-comparison.ts',
    name: 'screenshot-comparison',
    format: 'iife',
    globalName: 'ScreenshotComparisonModule',
  },
  {
    input: 'src/client/tf_model_bundle.mjs',
    name: 'tf-model-bundle',
    format: 'iife',
    globalName: 'TfModelBundleModule',
  },
  {
    input: 'src/client/utils/cross-tab-communicator.ts',
    name: 'cross-tab-communicator',
    format: 'iife',
    globalName: 'CrossTabCommunicatorModule',
  },
  {
    input: 'src/client/persistent-cdp-controller.ts',
    name: 'persistent-cdp-controller',
    format: 'iife',
    globalName: 'PersistentCDPControllerModule',
  },
];

async function buildClientFiles() {
  console.log('Building client files...');

  try {
    fs.mkdirSync('public/out', { recursive: true });
  } catch (error) {
    console.log('Output directory already exists or permission issue, continuing...');
  }

  const manifest = {};

  for (const file of clientFiles) {
    console.log(`Building ${file.name}...`);

    try {
      const result = await build({
        configFile: false,
        publicDir: false,
        build: {
          outDir: `public/out/temp-${file.name}`,
          emptyOutDir: true,
          sourcemap: false,
          minify: 'terser',
          terserOptions: {
            compress: {
              drop_console: false,
              drop_debugger: false,
            },
          },
          rollupOptions: {
            input: resolve(file.input),
            output: {
              format: file.format,
              entryFileNames: `${file.name}.js`,
              name: file.globalName,
              inlineDynamicImports: true,
            },
            external: [],
          },
        },

        esbuild: {
          target: 'es2018',
        },
        logLevel: 'warn',
      });

      const tempFilePath = `public/out/temp-${file.name}/${file.name}.js`;
      const content = fs.readFileSync(tempFilePath, 'utf8');

      const hash = generateContentHash(content);
      const hashedFileName = `${file.name}.${hash}.min.js`;

      const finalPath = `public/out/${hashedFileName}`;
      fs.writeFileSync(finalPath, content);

      manifest[`${file.name}.min.js`] = hashedFileName;

      fs.rmSync(`public/out/temp-${file.name}`, { recursive: true, force: true });

      console.log(`✓ Built ${file.name} -> ${hashedFileName}`);
    } catch (error) {
      console.error(`✗ Failed to build ${file.name}:`, error.message);
      process.exit(1);
    }
  }

  const manifestPath = 'public/out/manifest.json';
  fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2));

  const tsContent = `/**
 * This file is auto-generated during the build process.
 * It contains mappings from original filenames to content-hashed versions.
 * DO NOT EDIT MANUALLY.
 */

export const scriptManifest: Record<string, string> = ${JSON.stringify(manifest, null, 2)};`;

  fs.mkdirSync('src/browser', { recursive: true });
  fs.writeFileSync('src/browser/manifest.ts', tsContent);

  console.log('✓ All client files built successfully!');
  console.log('✓ Manifest files generated');
}

buildClientFiles().catch((error) => {
  console.error('Build failed:', error);
  process.exit(1);
});
