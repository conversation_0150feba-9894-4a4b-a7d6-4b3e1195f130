/**
 * CSS class generation utilities for form components
 */

import type { ButtonVariant } from '../types';

/**
 * Get CSS class for button based on variant
 */
export function getButtonClass(variant: ButtonVariant): string {
  switch (variant) {
    case 'primary':
      return 'button-primary';
    case 'secondary':
      return 'button-secondary';
    case 'link':
      return 'button-link';
    default:
      return 'button-primary';
  }
}
