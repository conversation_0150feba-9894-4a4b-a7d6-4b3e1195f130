#!/usr/bin/env node

import { build } from 'vite';
import { resolve } from 'path';
import crypto from 'crypto';
import fs from 'fs';
import chokidar from 'chokidar';

function generateContentHash(content) {
  return crypto.createHash('md5').update(content).digest('hex').substring(0, 8);
}

// Client files to build
const clientFiles = [
  {
    input: 'src/client/browser-controller-proxy.ts',
    name: 'browser-controller-proxy',
    format: 'iife',
    globalName: 'BrowserControllerProxyModule',
  },
  {
    input: 'src/client/screen-cropper.ts',
    name: 'screen-cropper',
    format: 'iife',
    globalName: 'ScreenCropperModule',
  },
  {
    input: 'src/client/captcha-detector.ts',
    name: 'captcha-detector',
    format: 'iife',
    globalName: 'CaptchaDetectorModule',
  },
  {
    input: 'src/client/captcha-detector-tf.mjs',
    name: 'captcha-detector-tf',
    format: 'iife',
    globalName: 'CaptchaDetectorTfModule',
  },
  {
    input: 'src/client/utils/screenshot-comparison.ts',
    name: 'screenshot-comparison',
    format: 'iife',
    globalName: 'ScreenshotComparisonModule',
  },
  {
    input: 'src/client/tf_model_bundle.mjs',
    name: 'tf-model-bundle',
    format: 'iife',
    globalName: 'TfModelBundleModule',
  },
  {
    input: 'src/client/cross-tab-communicator.ts',
    name: 'cross-tab-communicator',
    format: 'iife',
    globalName: 'CrossTabCommunicatorModule',
  },
  {
    input: 'src/client/utils/persistent-cdp-controller.ts',
    name: 'persistent-cdp-controller',
    format: 'iife',
    globalName: 'PersistentCDPControllerModule',
  },
];

// Create a map from file paths to build configs
const fileMap = new Map();
clientFiles.forEach((file) => {
  fileMap.set(resolve(file.input), file);
});

async function buildSingleFile(file) {
  console.log(`Building ${file.name}...`);

  try {
    const result = await build({
      // Disable config file to avoid conflicts
      configFile: false,
      // Disable public directory to avoid conflicts
      publicDir: false,

      // Build configuration
      build: {
        // Output to a temporary directory first
        outDir: `public/out/temp-${file.name}`,
        // Clean the temp directory
        emptyOutDir: true,
        // Generate source maps for debugging
        sourcemap: false,
        // Minify the output
        minify: 'terser',
        // Configure terser options
        terserOptions: {
          compress: {
            drop_console: false,
            drop_debugger: false,
          },
        },
        // Configure rollup options for single entry point
        rollupOptions: {
          input: resolve(file.input),
          output: {
            // Use specified format
            format: file.format,
            // Generate single file
            entryFileNames: `${file.name}.js`,
            // Configure global name for IIFE
            name: file.globalName,
            // Inline dynamic imports
            inlineDynamicImports: true,
          },
          // External dependencies (if any)
          external: [],
        },
      },

      // TypeScript configuration
      esbuild: {
        target: 'es2018',
      },

      // Resolve configuration
      resolve: {
        alias: {
          '@': resolve('src'),
          '@/client': resolve('src/client'),
          '@/common': resolve('src/common'),
        },
      },

      // Define global constants
      define: {
        __DEV__: JSON.stringify(process.env.NODE_ENV !== 'production'),
      },

      // Disable logging for cleaner output
      logLevel: 'warn',
    });

    // Read the generated file
    const tempFilePath = `public/out/temp-${file.name}/${file.name}.js`;
    const content = fs.readFileSync(tempFilePath, 'utf8');

    // Generate hash and create final filename
    const hash = generateContentHash(content);
    const hashedFileName = `${file.name}.${hash}.min.js`;

    // Write the final file
    const finalPath = `public/out/${hashedFileName}`;
    fs.writeFileSync(finalPath, content);

    // Clean up temp directory
    fs.rmSync(`public/out/temp-${file.name}`, { recursive: true, force: true });

    console.log(`✓ Built ${file.name} -> ${hashedFileName}`);

    // Update manifest
    updateManifest();
  } catch (error) {
    console.error(`✗ Failed to build ${file.name}:`, error.message);
  }
}

function updateManifest() {
  // Read all current files in public/out
  const files = fs.readdirSync('public/out').filter((f) => f.endsWith('.min.js'));
  const manifest = {};

  files.forEach((file) => {
    // Extract base name (remove hash and .min.js)
    const match = file.match(/^(.+)\.[a-f0-9]{8}\.min\.js$/);
    if (match) {
      const baseName = match[1];
      manifest[`${baseName}.min.js`] = file;
    }
  });

  // Write manifest.json
  const manifestPath = 'public/out/manifest.json';
  fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2));

  // Write TypeScript manifest file
  const tsContent = `/**
 * This file is auto-generated during the build process.
 * It contains mappings from original filenames to content-hashed versions.
 * DO NOT EDIT MANUALLY.
 */

export const scriptManifest: Record<string, string> = ${JSON.stringify(manifest, null, 2)};`;

  // Ensure the directory exists
  fs.mkdirSync('src/browser', { recursive: true });
  fs.writeFileSync('src/browser/manifest.ts', tsContent);
}

async function startWatching() {
  console.log('Starting Vite-powered client file watcher...');
  console.log('Watching for changes in src/client/**/*.{ts,mjs}');

  try {
    fs.mkdirSync('public/out', { recursive: true });
  } catch (error) {}

  console.log('Building all files initially...');
  for (const file of clientFiles) {
    await buildSingleFile(file);
  }

  console.log('✓ Initial build complete. Watching for changes...\n');

  const watcher = chokidar.watch('src/client/**/*.{ts,mjs}', {
    ignored: /node_modules/,
    persistent: true,
    ignoreInitial: true,
  });

  watcher.on('change', async (filePath) => {
    const absolutePath = resolve(filePath);
    const file = fileMap.get(absolutePath);

    if (file) {
      console.log(`\n📝 ${filePath} changed`);
      await buildSingleFile(file);
    } else {
      console.log(`\n📝 ${filePath} changed (dependency), rebuilding all...`);
      for (const file of clientFiles) {
        await buildSingleFile(file);
      }
    }
  });

  watcher.on('error', (error) => {
    console.error('Watcher error:', error);
  });

  process.on('SIGINT', () => {
    console.log('\n👋 Stopping watcher...');
    watcher.close();
    process.exit(0);
  });
}

startWatching().catch((error) => {
  console.error('Watch failed:', error);
  process.exit(1);
});
