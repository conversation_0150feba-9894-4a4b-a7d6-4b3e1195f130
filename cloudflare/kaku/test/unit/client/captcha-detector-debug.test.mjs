/**
 * Tests for the captcha detector debug panel functionality.
 * Tests image storage, comparison tracking, and debug panel operations.
 */

import { describe, it, expect, beforeAll, beforeEach, vi } from 'vitest';

// Mock the DOM environment
beforeAll(() => {
  // Define window if it doesn't exist in the test environment
  if (typeof window === 'undefined') {
    global.window = {
      location: { href: 'http://localhost:3000' },
      addEventListener: vi.fn(),
      postMessage: vi.fn(),
      parent: { postMessage: vi.fn() },
    };
  }

  // Define document if it doesn't exist
  if (typeof document === 'undefined') {
    global.document = {
      createElement: vi.fn(() => ({
        style: {},
        appendChild: vi.fn(),
        remove: vi.fn(),
        querySelector: vi.fn(),
        textContent: '',
        onclick: null,
        innerHTML: '',
      })),
      body: {
        appendChild: vi.fn(),
      },
    };
  }

  // Define performance if it doesn't exist
  if (typeof performance === 'undefined') {
    global.performance = {
      now: () => Date.now(),
      mark: vi.fn(),
      measure: vi.fn(),
      getEntriesByName: vi.fn(() => [{ duration: 10 }]),
      clearMarks: vi.fn(),
      clearMeasures: vi.fn(),
    };
  }

  // Mock canvas for base64 conversion
  global.HTMLCanvasElement = function () {
    return {
      width: 0,
      height: 0,
      getContext: vi.fn(() => ({
        putImageData: vi.fn(),
      })),
      toDataURL: vi.fn(() => 'data:image/png;base64,test'),
    };
  };
  global.HTMLCanvasElement.prototype = {
    getContext: vi.fn(() => ({
      putImageData: vi.fn(),
    })),
    toDataURL: vi.fn(() => 'data:image/png;base64,test'),
  };
  global.ImageData = function (data, width, height) {
    this.data = data;
    this.width = width;
    this.height = height;
  };
});

describe('Captcha Detector Debug Panel', () => {
  let captchaDetector;

  beforeEach(async () => {
    // Reset the global state
    delete window.captchaDetector;
    delete window.screenshotComparisonUtils;
    delete window.pixelmatch;

    // Mock screenshot comparison utilities
    window.screenshotComparisonUtils = {
      compareScreenshots: vi.fn(() => ({
        percentageDiff: 5.5,
        comparisonTime: 10.5,
        numDiffPixels: 100,
        totalPixels: 10000,
        dimensions: { width: 100, height: 100 },
        samplingUsed: 2,
      })),
    };

    // Mock screen cropper
    window.screenCropper = {
      registerCaptchaDetectorCallback: vi.fn(),
      startCapturingForCaptchaDetector: vi.fn(),
      stopCapturingForCaptchaDetector: vi.fn(),
      pauseFrameSending: vi.fn(),
      resumeFrameSending: vi.fn(),
    };

    // Create a simplified version of the captcha detector for testing
    let storedImages = new Map();
    let comparisonHistory = [];
    let imageCounter = 0;
    let debugPanel = null;

    function storeImageFrame(rgbaBuffer, dimensions) {
      imageCounter++;
      const alias = `image${imageCounter}`;
      const base64 = 'data:image/png;base64,test' + imageCounter;

      storedImages.set(alias, {
        base64,
        dimensions,
        timestamp: new Date().toISOString(),
        frameNumber: imageCounter,
      });

      return alias;
    }

    function storeComparisonResult(image1Alias, image2Alias, comparisonResult) {
      const comparison = {
        image1: image1Alias,
        image2: image2Alias,
        result: comparisonResult,
        timestamp: new Date().toISOString(),
        frameNumber: imageCounter,
      };

      comparisonHistory.push(comparison);
    }

    function createDebugPanel() {
      if (debugPanel) return;
      debugPanel = { id: 'captcha-debug-panel', created: true };
    }

    function hideDebugPanel() {
      debugPanel = null;
    }

    function updateDebugPanel() {
      // Mock update
    }

    captchaDetector = {
      storeImageFrame,
      storeComparisonResult,
      createDebugPanel,
      hideDebugPanel,
      updateDebugPanel,
      getStoredImages: () => storedImages,
      getComparisonHistory: () => comparisonHistory,
      clearDebugData: () => {
        storedImages.clear();
        comparisonHistory.length = 0;
        imageCounter = 0;
      },
    };

    window.captchaDetector = captchaDetector;
  });

  describe('Image Storage', () => {
    it('should store image frames with aliases', () => {
      const rgbaBuffer = new Uint8Array(400); // 10x10 RGBA
      const dimensions = { width: 10, height: 10 };

      const alias = captchaDetector.storeImageFrame(rgbaBuffer, dimensions);

      expect(alias).toBe('image1');

      const storedImages = captchaDetector.getStoredImages();
      expect(storedImages.size).toBe(1);
      expect(storedImages.has('image1')).toBe(true);

      const imageData = storedImages.get('image1');
      expect(imageData.dimensions).toEqual(dimensions);
      expect(imageData.base64).toBe('data:image/png;base64,test1');
      expect(imageData.frameNumber).toBe(1);
    });

    it('should generate sequential aliases for multiple images', () => {
      const rgbaBuffer = new Uint8Array(400);
      const dimensions = { width: 10, height: 10 };

      const alias1 = captchaDetector.storeImageFrame(rgbaBuffer, dimensions);
      const alias2 = captchaDetector.storeImageFrame(rgbaBuffer, dimensions);
      const alias3 = captchaDetector.storeImageFrame(rgbaBuffer, dimensions);

      expect(alias1).toBe('image1');
      expect(alias2).toBe('image2');
      expect(alias3).toBe('image3');

      const storedImages = captchaDetector.getStoredImages();
      expect(storedImages.size).toBe(3);
    });
  });

  describe('Comparison Tracking', () => {
    it('should store comparison results between images', () => {
      const comparisonResult = {
        percentageDiff: 5.5,
        comparisonTime: 10.5,
        numDiffPixels: 100,
        totalPixels: 10000,
        dimensions: { width: 100, height: 100 },
        samplingUsed: 2,
      };

      captchaDetector.storeComparisonResult('image1', 'image2', comparisonResult);

      const history = captchaDetector.getComparisonHistory();
      expect(history.length).toBe(1);

      const comparison = history[0];
      expect(comparison.image1).toBe('image1');
      expect(comparison.image2).toBe('image2');
      expect(comparison.result).toEqual(comparisonResult);
      expect(comparison.timestamp).toBeDefined();
    });

    it('should track multiple comparisons', () => {
      const result1 = { percentageDiff: 5.5, comparisonTime: 10.5, numDiffPixels: 100 };
      const result2 = { percentageDiff: 2.1, comparisonTime: 8.2, numDiffPixels: 50 };

      captchaDetector.storeComparisonResult('image1', 'image2', result1);
      captchaDetector.storeComparisonResult('image2', 'image3', result2);

      const history = captchaDetector.getComparisonHistory();
      expect(history.length).toBe(2);
      expect(history[0].result.percentageDiff).toBe(5.5);
      expect(history[1].result.percentageDiff).toBe(2.1);
    });
  });

  describe('Debug Panel Operations', () => {
    it('should create debug panel', () => {
      captchaDetector.createDebugPanel();
      // In a real implementation, you'd check if the panel was added to the DOM
      // For this test, we just verify the function can be called without error
      expect(captchaDetector.createDebugPanel).toBeDefined();
    });

    it('should hide debug panel', () => {
      captchaDetector.createDebugPanel();
      captchaDetector.hideDebugPanel();
      // Verify the function can be called without error
      expect(captchaDetector.hideDebugPanel).toBeDefined();
    });

    it('should clear debug data', () => {
      // Add some test data
      const rgbaBuffer = new Uint8Array(400);
      const dimensions = { width: 10, height: 10 };

      captchaDetector.storeImageFrame(rgbaBuffer, dimensions);
      captchaDetector.storeComparisonResult('image1', 'image2', { percentageDiff: 5.5 });

      expect(captchaDetector.getStoredImages().size).toBe(1);
      expect(captchaDetector.getComparisonHistory().length).toBe(1);

      // Clear the data
      captchaDetector.clearDebugData();

      expect(captchaDetector.getStoredImages().size).toBe(0);
      expect(captchaDetector.getComparisonHistory().length).toBe(0);
    });
  });
});
