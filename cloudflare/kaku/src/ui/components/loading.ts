import { html } from 'hono/html';
import { KazeelPlatformLogo } from './kazeel-platform-logo';

/**
 * Loading indicator component
 */
export const Loading = (props: {
  loadingText?: string;
  platformLogo?: string;
  platformName?: string;
}) => html`
  <div class="form-container animate-pulse p-8">
    <style>
      @keyframes shimmer {
        0% {
          background-position: -200px 0;
        }
        100% {
          background-position: calc(200px + 100%) 0;
        }
      }

      .shimmer {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200px 100%;
        animation: shimmer 1.5s infinite;
      }

      .dark .shimmer {
        background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
        background-size: 200px 100%;
      }
    </style>

    <div class="mb-8">
      ${KazeelPlatformLogo({
        platformLogo: props.platformLogo,
        platformName: props.platformName,
        variant: 'overlapping',
      })}
    </div>

    <div class="shimmer h-6 w-48 rounded mb-2 bg-neutral-20"></div>

    <div class="shimmer h-4 w-full rounded mb-6 bg-neutral-20"></div>
    <div class="shimmer h-4 w-3/4 rounded mb-6 bg-neutral-20"></div>

    <div class="input-container">
      <div class="floating-input-wrapper">
        <div class="shimmer h-12 w-full rounded-lg border bg-neutral-20"></div>
      </div>
    </div>

    <div class="input-container">
      <div class="floating-input-wrapper">
        <div class="shimmer h-12 w-full rounded-lg border bg-neutral-20"></div>
      </div>
    </div>

    <div class="button-container">
      <div class="shimmer h-10 w-full rounded-lg bg-neutral-20"></div>
    </div>

    ${props.loadingText
      ? html`
          <div class="flex items-center justify-center mt-4">
            <div class="animate-spin h-4 w-4 mr-2 text-primary">
              <svg viewBox="0 0 24 24" fill="none">
                <circle
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  stroke-width="4"
                  class="opacity-25"
                ></circle>
                <path
                  fill="currentColor"
                  class="opacity-75"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
            </div>
            <span class="text-sm text-neutral-60">${props.loadingText}</span>
          </div>
        `
      : ''}
  </div>
`;
