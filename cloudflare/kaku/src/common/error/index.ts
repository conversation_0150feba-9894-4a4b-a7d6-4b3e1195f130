// Error handling system exports
export { ErrorService, ErrorHandlers } from './ErrorService';
export { ErrorCollector } from './ErrorCollector';
export { ErrorRouter } from './ErrorRouter';
export type {
  ErrorContext,
  ProcessedError,
  ErrorSource,
  LogLevel,
  ErrorDisplayOptions,
} from './types';

// UI Components
export {
  ErrorDisplay,
  CriticalError,
  WarningBanner,
  InfoNotification,
  ErrorIconComponent,
  ErrorPageLayout,
  InvalidLinkErrorContent,
  RetryLimitExceededErrorContent,
} from '../../ui/components/error-display';
