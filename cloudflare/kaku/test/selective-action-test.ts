/**
 * Test script for selective action payload system
 * This script tests the new selective action filtering based on interaction type
 */

// Mock action data for testing
const mockActions = [
  {
    type: 'fill',
    name: 'login_field',
    value: '',
    coordinates: { x: 400, y: 200 },
    order: 1,
    isSubmitAction: false,
  },
  {
    type: 'fill',
    name: 'password',
    value: '',
    coordinates: { x: 400, y: 250 },
    order: 2,
    isSubmitAction: false,
  },
  {
    type: 'click',
    name: 'sign-in',
    value: '',
    coordinates: { x: 400, y: 300 },
    order: 3,
    isSubmitAction: true,
  },
  {
    type: 'click',
    name: 'continue-with-google',
    value: '',
    coordinates: { x: 400, y: 350 },
    order: 4,
    isSubmitAction: false,
  },
  {
    type: 'click',
    name: 'sign-in-with-passkey',
    value: '',
    coordinates: { x: 400, y: 400 },
    order: 5,
    isSubmitAction: false,
  },
];

const mockFormValues = {
  login: '<EMAIL>',
  password: 'testpassword123',
};

const mockFieldNameToIdMapping = {
  login: 'login_field',
  password: 'password',
};

// Simulate the filtering logic from connection-agent
function filterActionsByInteractionType(
  allActions: any[],
  interaction?: string,
  clickId?: string,
  formValues?: Record<string, string>,
  fieldNameToIdMapping?: Record<string, string>,
  extractionResult?: any,
): any[] {
  console.log(
    `[ACTION FILTER] Filtering ${allActions.length} actions for interaction type: ${interaction}`,
  );

  function actionCorrespondsToFormValue(action: any, formKey: string): boolean {
    // Direct match by action name (field.id)
    if (action.name === formKey) {
      return true;
    }

    // Check using field name to ID mapping
    if (fieldNameToIdMapping) {
      const fieldId = fieldNameToIdMapping[formKey];
      if (fieldId === action.name) {
        return true;
      }
    }

    return false;
  }

  function isActionForDontAskAgainControl(action: any): boolean {
    if (!extractionResult?.controls?.fields) {
      return false;
    }

    // Find the field that corresponds to this action
    const field = extractionResult.controls.fields.find((field: any) => field.id === action.name);

    return field?.isDontAskAgainControl === true;
  }

  switch (interaction) {
    case 'submit':
      const inputActions = allActions.filter((action) => {
        if (action.type === 'fill' || action.type === 'select') {
          const hasValue =
            formValues &&
            Object.keys(formValues).some((key) => {
              return actionCorrespondsToFormValue(action, key);
            });

          const isAIAction = isActionForDontAskAgainControl(action);

          return hasValue || isAIAction;
        }
        return false;
      });

      const submitAction = allActions.find(
        (action) => action.type === 'click' && action.name === clickId,
      );

      const result = [...inputActions];
      if (submitAction) {
        result.push(submitAction);
      }

      const aiActionCount = inputActions.filter((action) =>
        isActionForDontAskAgainControl(action),
      ).length;
      const regularActionCount = inputActions.length - aiActionCount;

      console.log(
        `[ACTION FILTER] Form submission: ${regularActionCount} input actions + ${aiActionCount} AI actions + ${submitAction ? 1 : 0} submit action`,
      );
      return result;

    case 'click':
      // For individual button clicks: only the specific button that was clicked
      const buttonAction = allActions.find(
        (action) => action.type === 'click' && action.name === clickId,
      );

      console.log(`[ACTION FILTER] Button click: ${buttonAction ? 1 : 0} action(s)`);
      return buttonAction ? [buttonAction] : [];

    case 'ai-action':
      // For AI actions: only the specific AI action
      const aiAction = allActions.find((action) => action.name === clickId);
      console.log(`[ACTION FILTER] AI action: ${aiAction ? 1 : 0} action(s)`);
      return aiAction ? [aiAction] : [];

    default:
      // Fallback: return all actions (legacy behavior)
      console.log(
        `[ACTION FILTER] Unknown interaction type, returning all ${allActions.length} actions`,
      );
      return allActions;
  }
}

function testFormSubmission() {
  console.log('\n🧪 Testing Form Submission (Submit Button Click)...');

  const filteredActions = filterActionsByInteractionType(
    mockActions,
    'submit',
    'sign-in',
    mockFormValues,
    mockFieldNameToIdMapping,
  );

  console.log(
    '✅ Filtered actions for form submission:',
    filteredActions.map((a) => `${a.type}:${a.name}`),
  );

  // Should include: login_field (fill), password (fill), sign-in (click)
  // Should NOT include: continue-with-google, sign-in-with-passkey
  const expectedActions = ['login_field', 'password', 'sign-in'];
  const actualActions = filteredActions.map((a) => a.name);

  const isCorrect =
    expectedActions.every((name) => actualActions.includes(name)) &&
    actualActions.length === expectedActions.length;

  console.log(`✅ Form submission test: ${isCorrect ? 'PASSED' : 'FAILED'}`);
  return isCorrect;
}

function testIndividualButtonClick() {
  console.log('\n🧪 Testing Individual Button Click (Continue with Google)...');

  const filteredActions = filterActionsByInteractionType(
    mockActions,
    'click',
    'continue-with-google',
    mockFormValues,
    mockFieldNameToIdMapping,
  );

  console.log(
    '✅ Filtered actions for button click:',
    filteredActions.map((a) => `${a.type}:${a.name}`),
  );

  // Should include: only continue-with-google (click)
  // Should NOT include: any input fields or other buttons
  const isCorrect =
    filteredActions.length === 1 &&
    filteredActions[0].name === 'continue-with-google' &&
    filteredActions[0].type === 'click';

  console.log(`✅ Individual button click test: ${isCorrect ? 'PASSED' : 'FAILED'}`);
  return isCorrect;
}

function testPasskeyButtonClick() {
  console.log('\n🧪 Testing Passkey Button Click...');

  const filteredActions = filterActionsByInteractionType(
    mockActions,
    'click',
    'sign-in-with-passkey',
    mockFormValues,
    mockFieldNameToIdMapping,
  );

  console.log(
    '✅ Filtered actions for passkey click:',
    filteredActions.map((a) => `${a.type}:${a.name}`),
  );

  // Should include: only sign-in-with-passkey (click)
  const isCorrect =
    filteredActions.length === 1 &&
    filteredActions[0].name === 'sign-in-with-passkey' &&
    filteredActions[0].type === 'click';

  console.log(`✅ Passkey button click test: ${isCorrect ? 'PASSED' : 'FAILED'}`);
  return isCorrect;
}

function runTests() {
  console.log('🚀 Starting Selective Action Payload Tests\n');

  const results = [testFormSubmission(), testIndividualButtonClick(), testPasskeyButtonClick()];

  const allPassed = results.every((result) => result);

  console.log(
    `\n${allPassed ? '✅' : '❌'} Overall result: ${allPassed ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED'}`,
  );

  if (allPassed) {
    console.log('\n🎉 Selective action payload system is working correctly!');
    console.log('✅ Form submissions will only execute input fields + submit button');
    console.log('✅ Individual button clicks will only execute that specific button');
    console.log('✅ No unintended cross-execution of unrelated form elements');
  }

  return allPassed;
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests();
}

export { runTests, filterActionsByInteractionType };
