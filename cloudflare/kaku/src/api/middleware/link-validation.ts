import { Context, Next } from 'hono';
import { ErrorPageLayout, InvalidLinkErrorContent } from '../../common/error';
import { <PERSON><PERSON><PERSON><PERSON> } from '../../common/types';
import { USER_ID, LINK_ID, PLATFORM_ID, COORDINATOR_STUB } from '../constants';

export async function linkValidationMiddleware(c: Context<KakuApp>, next: Next) {
  const userId = c.req.query(USER_ID) || 'u_hardCodedUserId'; // TODO: this will be removed once we have proper authentication
  const linkId = c.req.param()[LINK_ID];
  const platformId = c.req.param()[PLATFORM_ID];

  if (!linkId || !platformId) {
    return c.html(`<h1>Missing linkId or platformId</h1>`, 400);
  }

  const coordinator = c.env.CoordinatorDO.idFromName(userId); // TODO: pedro mentioned this ID can be different
  const coordinatorStub = c.env.CoordinatorDO.get(coordinator);
  const linkInfo = await coordinatorStub.getStatus(linkId);

  if (linkInfo?.status !== 'active') {
    return c.html(
      ErrorPageLayout({ title: 'Invalid Link Error', children: InvalidLinkErrorContent() }),
      500,
    );
  }

  c.set(USER_ID, userId);
  c.set(LINK_ID, linkId);
  c.set(PLATFORM_ID, platformId);
  c.set(COORDINATOR_STUB, coordinatorStub);

  await next();
}
