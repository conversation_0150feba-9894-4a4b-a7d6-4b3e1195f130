import * as fs from 'fs/promises';
import * as path from 'path';

export async function imageToBase64(filePath: string): Promise<string> {
    const absolutePath = path.resolve(filePath);
    const fileData = await fs.readFile(absolutePath);
    const dimensions = getImageDimensions(fileData);
    console.log(`Image width: ${dimensions.width}, height: ${dimensions.height}, size: ${fileData.length} bytes`)

    return fileData.toString('base64')
}

function getImageDimensions(buffer: Buffer): { width: number; height: number } {
  // PNG
  if (buffer.slice(0, 8).toString('hex') === '89504e470d0a1a0a') {
    return {
      width: buffer.readUInt32BE(16),
      height: buffer.readUInt32BE(20),
    };
  }

  // JPEG
  if (buffer.slice(0, 2).toString('hex') === 'ffd8') {
    let offset = 2;
    while (offset < buffer.length) {
      const marker = buffer.readUInt16BE(offset);
      offset += 2;

      const length = buffer.readUInt16BE(offset);
      if (
        marker >= 0xffc0 && marker <= 0xffcf &&
        marker !== 0xffc4 && marker !== 0xffc8 && marker !== 0xffcc
      ) {
        return {
          height: buffer.readUInt16BE(offset + 3),
          width: buffer.readUInt16BE(offset + 5),
        };
      }
      offset += length;
    }
  }

  // WebP
  if (buffer.toString('ascii', 0, 4) === 'RIFF' && buffer.toString('ascii', 8, 12) === 'WEBP') {
    const chunkHeader = buffer.toString('ascii', 12, 16);

    if (chunkHeader === 'VP8 ') {
      const width = buffer.readUInt16LE(26) & 0x3FFF;
      const height = buffer.readUInt16LE(28) & 0x3FFF;
      return { width, height };
    } else if (chunkHeader === 'VP8L') {
      const b0 = buffer[21];
      const b1 = buffer[22];
      const b2 = buffer[23];
      const b3 = buffer[24];
      const width = 1 + (((b1 & 0x3F) << 8) | b0);
      const height = 1 + (((b3 & 0xF) << 10) | (b2 << 2) | ((b1 & 0xC0) >> 6));
      return { width, height };
    } else if (chunkHeader === 'VP8X') {
      const width = 1 + buffer.readUIntLE(24, 3);
      const height = 1 + buffer.readUIntLE(27, 3);
      return { width, height };
    }
  }

  throw new Error('Unsupported or unknown image format');
}