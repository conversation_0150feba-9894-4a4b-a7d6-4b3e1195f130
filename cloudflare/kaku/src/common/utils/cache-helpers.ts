import crypto from 'crypto';
import { LLMRequest } from '../../llm/types/llm-request';
import { DetectPageStateChangeLlmRequest } from '../../llm/types/detect-page-state-change-llm-request';

/**
 * Generate a simple hash from a base64 image string
 * Returns first 8 characters of SHA-256 hash
 */
export function hashInfo(info: string): string {
  const hash = crypto.createHash('sha256');
  hash.update(info);
  return hash.digest('hex').substring(0, 8);
}

/**
 * Generate cache key for AI Gateway
 * Format: ${platform}_${imageHash}_${width}x${height}_${version}
 */
export function generateCacheKey(
  platform: string,
  imageHash: string,
  viewportWidth: number,
  viewportHeight: number,
  platformVersion: string,
  promptHash: string,
): string {
  return `${platform}_${imageHash}_${promptHash}_${viewportWidth}x${viewportHeight}_${platformVersion}`;
}

/**
 * Generate cache key from screenshot and parameters
 * Now supports dynamic cache versions via environment variables or direct version string
 */
export function generateCacheKeyFromScreenshot(
  llmRequest: LLMRequest,
  version: string = 'v1.0.0',
): string {
  const imageHash = hashInfo(llmRequest.screenshot);
  const promptHash = hashInfo(llmRequest.prompt);
  const width = llmRequest.viewportWidth;
  const height = llmRequest.viewportHeight;

  return generateCacheKey(llmRequest.platform, imageHash, width, height, version, promptHash);
}

export function generateCacheKeyForStateChangeLLMCalls(
    detectPageStateChangeLlmRequest: DetectPageStateChangeLlmRequest
): string {
  const imageHash = hashInfo(detectPageStateChangeLlmRequest.screenshot);
  const promptHash = hashInfo(detectPageStateChangeLlmRequest.prompt + JSON.stringify(detectPageStateChangeLlmRequest.agentVisionResultState));
  const width = detectPageStateChangeLlmRequest.viewportWidth;
  const height = detectPageStateChangeLlmRequest.viewportHeight;

  return generateCacheKey(detectPageStateChangeLlmRequest.platform, imageHash, width, height, detectPageStateChangeLlmRequest.version ?? 'v1.0.0', promptHash);
}