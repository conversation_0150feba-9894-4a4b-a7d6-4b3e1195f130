<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Captcha Detector Debug Panel Demo</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 20px;
        background: #f5f5f5;
      }
      .demo-container {
        max-width: 800px;
        margin: 0 auto;
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .demo-section {
        margin-bottom: 30px;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 5px;
      }
      button {
        background: #007cba;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        margin: 5px;
      }
      button:hover {
        background: #005a87;
      }
      .info {
        background: #e7f3ff;
        padding: 10px;
        border-radius: 5px;
        margin: 10px 0;
      }
      .status {
        font-weight: bold;
        color: #333;
      }
    </style>
  </head>
  <body>
    <div class="demo-container">
      <h1>🐛 Captcha Detector Debug Panel Demo</h1>

      <div class="info">
        <p><strong>Instructions:</strong></p>
        <ol>
          <li>Click "Initialize Captcha Detector" to set up the debug system</li>
          <li>Look for the 🐛 button in the bottom-right corner</li>
          <li>Use "Simulate Frame Capture" to add test images and comparisons</li>
          <li>Click the 🐛 button to open the debug panel and see stored data</li>
          <li>
            <strong>Close the panel and add more data - it will persist when reopened!</strong>
          </li>
          <li>Use "Clear Debug Data" to reset all stored images and comparisons</li>
        </ol>
      </div>

      <div class="demo-section">
        <h3>Captcha Detector Controls</h3>
        <button onclick="initializeCaptchaDetector()">Initialize Captcha Detector</button>
        <button onclick="initializeWithAutoPanel()">Initialize with Auto-Show Panel</button>
        <button onclick="initializeWithoutToggle()">Initialize without Toggle Button</button>
        <button onclick="cleanupCaptchaDetector()">Cleanup Captcha Detector</button>
        <div class="status" id="status">Status: Not initialized</div>
      </div>

      <div class="demo-section">
        <h3>Debug Panel Controls</h3>
        <button onclick="toggleDebugPanel()">Toggle Debug Panel</button>
        <button onclick="clearDebugData()">Clear Debug Data</button>
        <button onclick="simulateFrameCapture()">Simulate Frame Capture</button>
        <button onclick="simulateComparison()">Simulate Comparison</button>
        <button onclick="simulateMultipleFrames()">Simulate 5 Frames + Comparisons</button>
      </div>

      <div class="demo-section">
        <h3>Configuration Controls</h3>
        <button onclick="toggleDebugToggleButton()">Toggle Debug Button Visibility</button>
        <button onclick="showCurrentConfig()">Show Current Config</button>
        <button onclick="updateSamplingConfig()">Update Sampling to 4x</button>
      </div>

      <div class="demo-section">
        <h3>Debug Information</h3>
        <div id="debug-info">
          <p>Stored Images: <span id="image-count">0</span></p>
          <p>Comparisons: <span id="comparison-count">0</span></p>
        </div>
      </div>
    </div>

    <!-- Mock the required dependencies -->
    <script>
      // Mock screenshot comparison utilities
      window.screenshotComparisonUtils = {
        compareScreenshots: function (img1, img2, options) {
          return {
            percentageDiff: Math.random() * 10, // Random difference 0-10%
            comparisonTime: Math.random() * 20 + 5, // Random time 5-25ms
            numDiffPixels: Math.floor(Math.random() * 1000),
            totalPixels: 10000,
            dimensions: { width: 100, height: 100 },
            samplingUsed: options.sampling || 1,
          };
        },
      };

      // Mock screen cropper
      window.screenCropper = {
        registerCaptchaDetectorCallback: function (callback) {
          console.log('Registered captcha detector callback');
        },
        startCapturingForCaptchaDetector: function () {
          console.log('Started capturing for captcha detector');
        },
        stopCapturingForCaptchaDetector: function () {
          console.log('Stopped capturing for captcha detector');
        },
        pauseFrameSending: function () {
          console.log('Paused frame sending');
        },
        resumeFrameSending: function () {
          console.log('Resumed frame sending');
        },
      };

      let isInitialized = false;
      let simulationCounter = 0;

      function updateStatus(message) {
        document.getElementById('status').textContent = 'Status: ' + message;
      }

      function updateDebugInfo() {
        if (window.captchaDetector) {
          const images = window.captchaDetector.getStoredImages();
          const comparisons = window.captchaDetector.getComparisonHistory();
          document.getElementById('image-count').textContent = images.size;
          document.getElementById('comparison-count').textContent = comparisons.length;
        }
      }

      function initializeCaptchaDetector() {
        if (window.captchaDetector) {
          window.captchaDetector.initialize({ debug: true });
          isInitialized = true;
          updateStatus('Initialized with debug mode');
          updateDebugInfo();
        } else {
          updateStatus('Error: Captcha detector not loaded');
        }
      }

      function initializeWithAutoPanel() {
        if (window.captchaDetector) {
          window.captchaDetector.initialize({
            debug: true,
            autoShowDebugPanel: true,
            showDebugToggle: true,
          });
          isInitialized = true;
          updateStatus('Initialized with auto-show debug panel');
          updateDebugInfo();
        } else {
          updateStatus('Error: Captcha detector not loaded');
        }
      }

      function initializeWithoutToggle() {
        if (window.captchaDetector) {
          window.captchaDetector.initialize({
            debug: true,
            showDebugToggle: false,
            autoShowDebugPanel: false,
          });
          isInitialized = true;
          updateStatus('Initialized without toggle button');
          updateDebugInfo();
        } else {
          updateStatus('Error: Captcha detector not loaded');
        }
      }

      function cleanupCaptchaDetector() {
        if (window.captchaDetector) {
          window.captchaDetector.cleanup();
          isInitialized = false;
          updateStatus('Cleaned up');
          updateDebugInfo();
        }
      }

      function toggleDebugPanel() {
        if (window.captchaDetector) {
          // Check if panel exists by trying to find it
          const panel = document.getElementById('captcha-debug-panel');
          if (panel) {
            window.captchaDetector.hideDebugPanel();
          } else {
            window.captchaDetector.createDebugPanel();
          }
        }
      }

      function clearDebugData() {
        if (window.captchaDetector) {
          window.captchaDetector.clearDebugData();
          updateDebugInfo();
        }
      }

      function simulateFrameCapture() {
        if (window.captchaDetector) {
          simulationCounter++;
          // Create a mock RGBA buffer (small 10x10 image)
          const rgbaBuffer = new Uint8Array(400); // 10x10 * 4 bytes per pixel

          // Fill with some random data to simulate different frames
          for (let i = 0; i < rgbaBuffer.length; i += 4) {
            rgbaBuffer[i] = Math.floor(Math.random() * 255); // R
            rgbaBuffer[i + 1] = Math.floor(Math.random() * 255); // G
            rgbaBuffer[i + 2] = Math.floor(Math.random() * 255); // B
            rgbaBuffer[i + 3] = 255; // A (fully opaque)
          }

          const dimensions = { width: 10, height: 10 };
          const alias = window.captchaDetector.storeImageFrame(rgbaBuffer, dimensions);
          console.log('Simulated frame capture:', alias);
          updateDebugInfo();
        }
      }

      function simulateComparison() {
        if (window.captchaDetector) {
          const images = window.captchaDetector.getStoredImages();
          if (images.size >= 2) {
            const imageAliases = Array.from(images.keys());
            const img1 = imageAliases[imageAliases.length - 2];
            const img2 = imageAliases[imageAliases.length - 1];

            const mockResult = {
              percentageDiff: Math.random() * 10,
              comparisonTime: Math.random() * 20 + 5,
              numDiffPixels: Math.floor(Math.random() * 100),
              totalPixels: 100,
              dimensions: { width: 10, height: 10 },
              samplingUsed: 1,
            };

            window.captchaDetector.storeComparisonResult(img1, img2, mockResult);
            console.log('Simulated comparison:', img1, 'vs', img2);
            updateDebugInfo();
          } else {
            alert(
              'Need at least 2 stored images to simulate a comparison. Click "Simulate Frame Capture" first.',
            );
          }
        }
      }

      function simulateMultipleFrames() {
        if (window.captchaDetector) {
          // Simulate 5 frames with automatic comparisons
          for (let i = 0; i < 5; i++) {
            setTimeout(() => {
              simulateFrameCapture();

              // After the second frame, start doing comparisons
              if (i > 0) {
                setTimeout(() => {
                  simulateComparison();
                }, 100);
              }
            }, i * 300); // Stagger the frames
          }

          console.log('Simulating 5 frames with comparisons...');
        }
      }

      function toggleDebugToggleButton() {
        if (window.captchaDetector) {
          const currentConfig = window.captchaDetector.getConfig();
          const newVisibility = !currentConfig.showDebugToggle;
          window.captchaDetector.setDebugToggleVisibility(newVisibility);
          updateStatus(`Debug toggle button ${newVisibility ? 'shown' : 'hidden'}`);
        }
      }

      function showCurrentConfig() {
        if (window.captchaDetector) {
          const config = window.captchaDetector.getConfig();
          alert('Current Configuration:\n' + JSON.stringify(config, null, 2));
        }
      }

      function updateSamplingConfig() {
        if (window.captchaDetector) {
          window.captchaDetector.updateConfig({ sampling: 4 });
          updateStatus('Updated sampling to 4x');
        }
      }

      // Auto-update debug info every second
      setInterval(updateDebugInfo, 1000);
    </script>

    <!-- Load the captcha detector script -->
    <script src="src/client/captcha-detector.mjs"></script>
  </body>
</html>
