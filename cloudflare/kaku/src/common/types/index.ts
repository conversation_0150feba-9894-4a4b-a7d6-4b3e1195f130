import { z } from 'zod';
import { CoordinatorDO } from '../../coordinator';
import { User } from '../../user/User';

export type KakuApp = {
  Bindings: Env;
  Variables: {
    user?: User;
    userId: string;
    linkId: string;
    platformId: string;
    coordinatorStub: DurableObjectStub<CoordinatorDO>;
  };
};

export const TokenInfoSchema = z.object({
  xAuthToken: z.string(),
  csrfToken: z.string(),
});

export type TokenInfo = z.infer<typeof TokenInfoSchema>;
export * from './common';
