import { FormVisionResult } from '../../src/form-generation/types';

export const facebookLogin: FormVisionResult = {
  screenInfo: {
    title: 'facebook',
    description: 'Facebook helps you connect and share with the people in your life.',
    instruction: 'Please enter your email address or phone number and password to log in.',
    verificationCode: null,
    errors: [],
    type: 'not-authenticated',
  },
  controls: {
    fields: [
      {
        id: 'email-address',
        order: 1,
        actor: 'human',
        label: 'Email address or phone number',
        fieldControlType: 'text',
        actiontype: 'fill',
        name: 'email',
        options: null,
        readOnly: false,
        box_2d: [223, 573, 295, 827],
      },
      {
        id: 'password',
        order: 2,
        actor: 'human',
        label: 'Password',
        fieldControlType: 'password',
        actiontype: 'fill',
        name: 'pass',
        options: null,
        readOnly: false,
        box_2d: [313, 573, 385, 827],
      },
    ],
    buttons: [
      {
        id: 'login',
        order: 3,
        actor: 'human',
        label: 'Log in',
        variant: 'primary',
        type: 'submit',
        actiontype: 'click',
        synthetic: false,
        box_2d: [404, 573, 476, 827],
      },
      {
        id: 'forgotten-password',
        order: 4,
        actor: 'human',
        label: 'Forgotten password?',
        variant: 'link',
        type: 'click',
        actiontype: 'click',
        synthetic: false,
        box_2d: [495, 633, 520, 767],
      },
      {
        id: 'create-account',
        order: 5,
        actor: 'human',
        label: 'Create new account',
        variant: 'secondary',
        type: 'click',
        actiontype: 'click',
        synthetic: false,
        box_2d: [595, 633, 667, 767],
      },
    ],
  },
};

export const googleLogin: FormVisionResult = {
  screenInfo: {
    title: 'Sign in',
    description: 'Use your Google Account',
    instruction: 'Enter your email or phone number to continue.',
    verificationCode: null,
    errors: [],
    type: 'not-authenticated',
  },
  controls: {
    fields: [
      {
        id: 'identifierId',
        order: 1,
        actor: 'human',
        label: 'Email or phone',
        fieldControlType: 'text',
        actiontype: 'fill',
        name: 'identifierId',
        options: null,
        readOnly: false,
        box_2d: [325, 516, 412, 838],
      },
    ],
    buttons: [
      {
        id: 'identifierNext',
        order: 2,
        actor: 'human',
        label: 'Next',
        variant: 'primary',
        type: 'submit',
        actiontype: 'click',
        synthetic: false,
        box_2d: [637, 783, 695, 837],
      },
    ],
  },
};

export const githubLogin: FormVisionResult = {
  screenInfo: {
    title: 'Sign in to GitHub',
    description: '',
    instruction: 'Enter your username or email address and password.',
    verificationCode: null,
    errors: [],
    type: 'not-authenticated',
  },
  controls: {
    fields: [
      {
        id: 'login_field',
        order: 1,
        actor: 'human',
        label: 'Username or email address',
        fieldControlType: 'text',
        actiontype: 'fill',
        name: 'login',
        options: null,
        readOnly: false,
        box_2d: [299, 414, 347, 589],
      },
    ],
    buttons: [
      {
        id: 'password',
        order: 2,
        actor: 'human',
        label: 'Password',
        type: 'click',
        actiontype: 'click',
        box_2d: [400, 414, 448, 589],
        variant: 'primary',
        synthetic: false,
      },
      {
        id: 'sign-in',
        order: 3,
        actor: 'human',
        label: 'Sign in',
        variant: 'primary',
        type: 'submit',
        actiontype: 'click',
        synthetic: false,
        box_2d: [484, 414, 532, 589],
      },
      {
        id: 'sign-in-with-passkey',
        order: 4,
        actor: 'human',
        label: 'Sign in with a passkey',
        variant: 'secondary',
        type: 'click',
        actiontype: 'click',
        synthetic: false,
        box_2d: [577, 401, 625, 602],
      },
      {
        id: 'create-account',
        order: 5,
        actor: 'human',
        label: 'Create an account',
        variant: 'link',
        type: 'click',
        actiontype: 'click',
        synthetic: false,
        box_2d: [645, 497, 666, 580],
      },
    ],
  },
};
