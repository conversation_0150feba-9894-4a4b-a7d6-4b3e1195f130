/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['src/ui/**/*.ts', 'src/ui/*.css', 'src/main.css', 'src/**/*.ts'],
  theme: {
    extend: {
      colors: {
        primary: {
          0: '#000000',
          5: '#16023E',
          10: '#210F48',
          15: '#2B1B53',
          20: '#36265E',
          25: '#41326A',
          30: '#4D3D76',
          35: '#594983',
          40: '#655590',
          50: '#7E6DAB',
          60: '#9887C6',
          70: '#B4A1E2',
          80: '#CFBCFF',
          90: '#E9DDFF',
          95: '#F6EEFF',
          98: '#FDF7FF',
          99: '#FFFBFF',
          100: '#FFFFFF',
          DEFAULT: '#65558F',
          container: '#E9DDFF',
          'on-primary': '#FFFFFF',
          'on-container': '#4D3D75',
          dark: '#CFBDFE',
          'dark-container': '#4D3D75',
          'dark-on-primary': '#36275D',
          'dark-on-container': '#E9DDFF',
        },

        secondary: {
          0: '#000000',
          5: '#120F1C',
          10: '#1D1A27',
          15: '#282431',
          20: '#322E3C',
          25: '#3E3948',
          30: '#494453',
          35: '#55505F',
          40: '#615C6C',
          50: '#7A7485',
          60: '#948E9F',
          70: '#AFA8BA',
          80: '#CBC3D6',
          90: '#E7DFF2',
          95: '#F6EEFF',
          98: '#FDF7FF',
          99: '#FFFBFF',
          100: '#FFFFFF',
          DEFAULT: '#625B71', // Light secondary
          container: '#E8DEF8', // Light secondary container
          'on-secondary': '#FFFFFF',
          'on-container': '#4A4458',
          dark: '#CCC2DB', // Dark secondary
          'dark-container': '#4A4458', // Dark secondary container
          'dark-on-secondary': '#332D41',
          'dark-on-container': '#E8DEF8',
        },

        tertiary: {
          0: '#000000',
          5: '#200912',
          10: '#2D141D',
          15: '#381E27',
          20: '#442831',
          25: '#50333C',
          30: '#5D3E48',
          35: '#6A4953',
          40: '#77555F',
          50: '#916D78',
          60: '#AD8691',
          70: '#C9A1AC',
          80: '#E6BBC7',
          90: '#FFD9E3',
          95: '#FFECF0',
          98: '#FFF8F8',
          99: '#FFFBFF',
          100: '#FFFFFF',
          DEFAULT: '#7E5260',
          container: '#FFD9E3',
          'on-tertiary': '#FFFFFF',
          'on-container': '#633B48',
          dark: '#EFB8C8',
          'dark-container': '#633B48',
          'dark-on-tertiary': '#4A2532',
          'dark-on-container': '#FFD9E3',
        },

        error: {
          DEFAULT: '#BA1A1A',
          container: '#FFDAD6',
          'on-error': '#FFFFFF',
          'on-container': '#93000A',
          dark: '#FFB4AB',
          'dark-container': '#93000A',
          'dark-on-error': '#690005',
          'dark-on-container': '#FFDAD6',
        },

        neutral: {
          0: '#000000',
          5: '#121013',
          10: '#1C1B1E',
          15: '#272528',
          20: '#313032',
          25: '#3D3B3E',
          30: '#484649',
          35: '#545255',
          40: '#605D60',
          50: '#797679',
          60: '#939093',
          70: '#AEAAAD',
          80: '#C9C5C9',
          90: '#E6E1E4',
          95: '#F4EFF3',
          98: '#FDF8FB',
          99: '#FFFBFF',
          100: '#FFFFFF',
        },

        'neutral-variant': {
          0: '#000000',
          5: '#121016',
          10: '#1D1B20',
          15: '#27252B',
          20: '#322F36',
          25: '#3D3A41',
          30: '#48464C',
          35: '#545158',
          40: '#605D64',
          50: '#79767D',
          60: '#938F97',
          70: '#AEA9B1',
          80: '#CAC5CD',
          90: '#E6E0E9',
          95: '#F5EFF7',
          98: '#FDF7FF',
          99: '#FFFBFF',
          100: '#FFFFFF',
        },

        surface: {
          DEFAULT: '#FDF7FF',
          dim: '#DED8E0',
          bright: '#FDF7FF',
          'container-lowest': '#FFFFFF',
          'container-low': '#F8F2FA',
          container: '#F2ECF4',
          'container-high': '#ECE6EE',
          'container-highest': '#E6E0E9',
          variant: '#E7E0EB',
          tint: '#65558F',
          'on-surface': '#1D1B20',
          'on-variant': '#49454E',
          dark: '#141218',
          'dark-dim': '#141218',
          'dark-bright': '#3B383E',
          'dark-container-lowest': '#0F0D13',
          'dark-container-low': '#1D1B20',
          'dark-container': '#211F24',
          'dark-container-high': '#2B292F',
          'dark-container-highest': '#36343A',
          'dark-variant': '#49454E',
          'dark-tint': '#CFBDFE',
          'dark-on-surface': '#E6E0E9',
          'dark-on-variant': '#CAC4CF',
        },

        background: {
          DEFAULT: '#FDF7FF',
          'on-background': '#1D1B20',
          dark: '#141218',
          'dark-on-background': '#E6E0E9',
        },

        outline: {
          DEFAULT: '#7A757F',
          variant: '#CAC4CF',
          dark: '#948F99',
          'dark-variant': '#49454E',
        },

        shadow: '#000000',
        scrim: '#000000',

        inverse: {
          surface: '#322F35',
          'on-surface': '#F5EFF7',
          primary: '#CFBDFE',
          'dark-surface': '#E6E0E9',
          'dark-on-surface': '#322F35',
          'dark-primary': '#65558F',
        },
      },
    },
  },
  plugins: [],
};
