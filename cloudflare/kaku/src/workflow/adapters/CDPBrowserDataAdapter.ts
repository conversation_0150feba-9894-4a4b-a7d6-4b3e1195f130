import { <PERSON><PERSON> } from 'hono/utils/cookie';
import { CDP } from '../../browser/simple-cdp';
import { BrowserDataAdapter } from './BrowserDataAdapter';

/**
 * CDP implementation of BrowserDataAdapter
 * Handles all Chrome DevTools Protocol communication for browser data operations
 */
export class CDPBrowserDataAdapter implements BrowserDataAdapter {
  constructor(
    private cdpClient: CDP,
    private sessionId?: string,
  ) {}

  async getCookies(): Promise<Cookie[]> {
    const response = await this.cdpClient.Storage.getCookies({}, this.sessionId);
    return response.cookies;
  }

  async getLocalStorageData(): Promise<Record<string, string | null>> {
    const response = await this.cdpClient.Runtime.evaluate(
      {
        expression: `(() => {
          const data = {};
          for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key) {
              data[key] = localStorage.getItem(key);
            }
          }
          return data;
        })()`,
        returnByValue: true,
      },
      this.sessionId,
    );

    return response.result.value as Record<string, string | null>;
  }

  async getSessionStorageData(): Promise<Record<string, string | null>> {
    const response = await this.cdpClient.Runtime.evaluate(
      {
        expression: `(() => {
          const data = {};
          for (let i = 0; i < sessionStorage.length; i++) {
            const key = sessionStorage.key(i);
            if (key) {
              data[key] = sessionStorage.getItem(key);
            }
          }
          return data;
        })()`,
        returnByValue: true,
      },
      this.sessionId,
    );

    return response.result.value as Record<string, string | null>;
  }

  async setCookies(cookies: Cookie[]): Promise<void> {
    const cookieParams = cookies.map((cookie) => {
      const {
        name,
        value,
        domain,
        path,
        expires,
        httpOnly,
        secure,
        sameSite,
        priority,
        sameParty,
        sourceScheme,
      } = cookie;

      const param: any = {
        name,
        value,
        url: undefined,
        domain,
        path,
        secure,
        httpOnly,
        sameSite,
        expires,
        priority,
        sameParty,
        sourceScheme,
      };

      return param;
    });

    await this.cdpClient.Storage.setCookies({ cookies: cookieParams }, this.sessionId);
  }

  async setLocalStorageData(data: Record<string, string | null>): Promise<void> {
    if (Object.keys(data).length === 0) return;

    const script = `
      const data = ${JSON.stringify(data)};
      for (const [key, value] of Object.entries(data)) {
        if (value !== null) {
          localStorage.setItem(key, value);
        }
      }
    `;

    await this.cdpClient.Runtime.evaluate(
      {
        expression: script,
      },
      this.sessionId,
    );
  }

  async setSessionStorageData(data: Record<string, string | null>): Promise<void> {
    if (Object.keys(data).length === 0) return;

    const script = `
      const data = ${JSON.stringify(data)};
      for (const [key, value] of Object.entries(data)) {
        if (value !== null) {
          sessionStorage.setItem(key, value);
        }
      }
    `;

    await this.cdpClient.Runtime.evaluate(
      {
        expression: script,
      },
      this.sessionId,
    );
  }
}
