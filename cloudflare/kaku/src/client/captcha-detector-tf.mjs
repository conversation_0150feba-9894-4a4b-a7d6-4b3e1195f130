(function () {
  /**
   * TensorFlow.js Captcha Detector with 2-Class Captcha Model Support
   *
   * This detector supports a 3-class model but only cares about captcha classes:
   * - captcha_unresolved (index 1): Active captchas that need to be solved
   * - captcha_resolved (index 2): Solved/completed captchas
   * - background (index 0): Ignored - we don't process background detections
   *
   * Configuration Options:
   * - detectUnresolved: Boolean, whether to detect unresolved captchas (default: true)
   * - detectResolved: Boolean, whether to detect resolved captchas (default: false)
   * - preferredClass: String, which class to prefer when multiple detections exist
   *   Options: 'unresolved', 'resolved', 'both' (default: 'unresolved')
   *
   * The detector provides detailed logging with separate statistics for each captcha class,
   * including detection counts, max scores, average scores, and top 5 scores.
   *
   * Model Output Format:
   * - result[0]: scores tensor [1, N, 3] where N is number of boxes, 3 is number of classes
   * - result[1]: boxes tensor [N, 4] with normalized coordinates [ymin, xmin, ymax, xmax]
   */

  const DEFAULT_CONFIG = {
    scoreThreshold: 0.1,
    debug: true,
    inputSize: 640, // Default input size for the model
  };

  // Class indices for the 2-class model (we only care about captcha classes)
  const CLASS_INDICES = {
    captcha_unresolved: 2,
    captcha_resolved: 3,
  };

  const CLASS_NAMES = ['captcha_unresolved', 'captcha_resolved'];

  let config = { ...DEFAULT_CONFIG };
  let model = null;
  let isModelLoaded = false;
  let isModelLoading = false;

  function error(...args) {
    console.error('[kazeel][captcha-detector-tf]', ...args);
  }

  /**
   * Initialize the TensorFlow detector with the provided configuration.
   * @param {Object} options - Configuration options
   * @returns {Promise<boolean>} - True if initialization was successful
   */
  async function initialize(options = {}) {
    if (isModelLoaded) return true;
    if (isModelLoading) return false;

    Object.assign(config, options);

    if (config.debug) {
      console.log('[TF Detector] Initializing with config:', JSON.stringify(config));
    }

    try {
      isModelLoading = true;

      // Ensure TensorFlow.js is loaded
      if (!window.tf) {
        console.log('[TF Detector] TensorFlow.js not found, loading...');
        await loadTensorFlow();
      }

      // Load the model from memory via the registered tf.io router.
      console.log(`[TF Detector] Loading model from in-memory source`);
      model = await tf.loadGraphModel('inmemory://model');
      isModelLoaded = true;

      console.log('[TF Detector] Model loaded!!!!');

      // Warm up the model
      const dummyTensor = tf.zeros([1, config.inputSize, config.inputSize, 3], 'float32');
      let warmupResult;
      if (model.executeAsync) {
        warmupResult = await model.executeAsync({ ToFloat: dummyTensor });
      } else {
        warmupResult = model.predict({ ToFloat: dummyTensor });
      }
      if (Array.isArray(warmupResult)) {
        warmupResult.forEach((tensor) => tensor && tensor.dispose && tensor.dispose());
      } else if (warmupResult && warmupResult.dispose) {
        warmupResult.dispose();
      }
      dummyTensor.dispose();

      isModelLoaded = true;
      isModelLoading = false;

      console.log('[TF Detector] Model loaded and ready');
      return true;
    } catch (error) {
      isModelLoading = false;
      throw new Error(`[kazeel][captcha-detector-tf] Failed to initialize TF model: ${error}`);
    }
  }

  async function getInitialBoundingBox({ width, height }) {
    const detectionResults = await takeScreenshotAndDetect({ width: width, height: height });
    if (config.debug) {
      console.log('[TF Detector] Warm up detections length:', detectionResults.detections.length);
    }
    if (detectionResults.detections.length > 0) {
      const bestBoundingBox = findBestCaptchaBoundingBox(detectionResults.detections);
      console.log('[TF Detector] Warm up bounding box:', bestBoundingBox);
      return padCropBox(bestBoundingBox);
    }
    throw new Error(
      `[kazeel][captcha-detector-tf] Failed to get initial bounding box, check the score threshold or make sure the captcha is visible: ${err}`,
    );
  }

  function padCropBox(cropBox, padding = 20) {
    const x = Math.max(0, cropBox.x - padding);
    const y = Math.max(0, cropBox.y - padding);

    const xPadUsed = cropBox.x - x;
    const yPadUsed = cropBox.y - y;

    const width = cropBox.width + xPadUsed + padding;
    const height = cropBox.height + yPadUsed + padding;

    return { x, y, width, height };
  }

  /**
   * Load TensorFlow.js library dynamically
   * @returns {Promise<void>}
   */
  async function loadTensorFlow() {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js';
      script.onload = () => {
        console.log('[TF Detector] TensorFlow.js loaded');
        resolve();
      };
      script.onerror = (err) => {
        error(`Failed to load TensorFlow.js: ${err}`);
        reject(err);
      };
      document.head.appendChild(script);
    });
  }

  /**
   * Process an RGBA buffer to detect objects
   * @param {Uint8ClampedArray} rgbaBuffer - RGBA pixel data
   * @param {Object} dimensions - {width, height} of the image
   * @returns {Promise<Object>} - Detection results with bounding boxes
   */
  async function detectObjects(rgbaBuffer, dimensions) {
    if (!isModelLoaded) {
      throw new Error(`[kazeel][captcha-detector-tf] Model not loaded, cannot detect objects`);
    }

    const startTime = performance.now();

    try {
      const inputTensor = await rgbaToTensor(rgbaBuffer, dimensions);
      const result = await runInference(inputTensor);

      // Process results
      const processedResults = processDetections(result, dimensions);

      // Clean up tensors
      inputTensor.dispose();
      if (Array.isArray(result)) {
        result.forEach((tensor) => {
          if (tensor && typeof tensor.dispose === 'function') {
            tensor.dispose();
          }
        });
      } else if (result && typeof result === 'object') {
        Object.values(result).forEach((tensor) => {
          if (tensor && typeof tensor.dispose === 'function') {
            tensor.dispose();
          }
        });
      }

      const endTime = performance.now();
      const latency = endTime - startTime;

      if (config.debug) {
        console.log(`[TF Detector] Detection completed in ${latency.toFixed(2)}ms`);
        console.log(`[TF Detector] Found ${processedResults.detections.length} objects`);
      }

      return {
        ...processedResults,
        latency,
      };
    } catch (error) {
      error(`Error during detecting objects: ${error}`);
      return { detections: [], latency: 0 };
    }
  }

  /**
   * Convert RGBA buffer to a TensorFlow tensor, normalized and resized
   * @param {Uint8ClampedArray} rgbaBuffer - RGBA pixel data
   * @param {Object} dimensions - {width, height} of the image
   * @returns {tf.Tensor} - Processed tensor ready for the model
   */
  async function rgbaToTensor(rgbaBuffer, dimensions) {
    const { width, height } = dimensions;

    // Always clone to base view
    const arr = new Uint8ClampedArray(rgbaBuffer);
    if (arr.length !== width * height * 4) {
      throw new Error(
        `[kazeel][captcha-detector-tf] RGBA buffer size mismatch: expected ${width * height * 4}, got ${arr.length}`,
      );
    }
    const imageData = new ImageData(arr, width, height);

    // Canvas to RGB (strips alpha)
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;
    const ctx = canvas.getContext('2d');
    ctx.putImageData(imageData, 0, 0);

    return tf.tidy(() => {
      let tensor = tf.browser.fromPixels(canvas); // shape [h, w, 3], dtype int32
      // Resize if required
      if (width !== config.inputSize || height !== config.inputSize) {
        const resized = tf.image.resizeBilinear(tensor, [config.inputSize, config.inputSize]);
        tensor.dispose();
        tensor = resized;
      }
      // Convert to float32 and normalize to [0,1]
      tensor = tensor.toFloat();
      // Add batch dimension: shape [1, h, w, 3]
      tensor = tensor.expandDims(0);
      return tensor;
    });
  }

  /**
   * Run inference with the TensorFlow model
   * @param {tf.Tensor} inputTensor - Input tensor
   * @returns {Object|Array} - Raw model output
   */
  async function runInference(inputTensor) {
    if (model.executeAsync) {
      return await model.executeAsync({ ToFloat: inputTensor });
    } else if (model.predict) {
      return model.predict({ ToFloat: inputTensor });
    } else {
      throw new Error(`[kazeel][captcha-detector-tf] Failed to run inference, model is not ready`);
    }
  }

  /**
   * Process the raw model output into usable detections
   * @param {Array} result - Raw model output
   * @param {Object} dimensions - Original image dimensions
   * @returns {Object} - Processed detections with separate counts for each class
   */
  function processDetections(result, dimensions) {
    const { width, height } = dimensions;
    let scoresTensor, boxesTensor;
    if (Array.isArray(result) && result.length === 2) {
      scoresTensor = result[0]; // [1, N, 3] - now 3 classes
      boxesTensor = result[1]; // [N, 4]
    } else {
      throw new Error(`[kazeel][captcha-detector-tf] Unexpected model output format`);
    }

    // Get arrays
    const scores = scoresTensor.arraySync(); // shape [1, N, 3]
    const boxes = boxesTensor.arraySync(); // shape [N, 4]

    const detections = [];
    const numBoxes = boxes.length;

    // Track statistics for each class
    const stats = {
      captcha_unresolved: { count: 0, maxScore: 0, avgScore: 0, totalScore: 0 },
      captcha_resolved: { count: 0, maxScore: 0, avgScore: 0, totalScore: 0 },
    };

    // Collect all scores for analysis
    const allScores = {
      captcha_unresolved: [],
      captcha_resolved: [],
    };

    // Track highest scoring detection across all classes
    let highestScoringDetection = null;

    for (let i = 0; i < numBoxes; i++) {
      const boxScores = scores[0][i]; // [background_score, unresolved_score, resolved_score]
      const unresolvedScore = boxScores[CLASS_INDICES.captcha_unresolved];
      const resolvedScore = boxScores[CLASS_INDICES.captcha_resolved];

      // Collect scores for statistics (only captcha classes)
      allScores.captcha_unresolved.push(unresolvedScore);
      allScores.captcha_resolved.push(resolvedScore);

      // Update max scores
      if (unresolvedScore > stats.captcha_unresolved.maxScore)
        stats.captcha_unresolved.maxScore = unresolvedScore;
      if (resolvedScore > stats.captcha_resolved.maxScore)
        stats.captcha_resolved.maxScore = resolvedScore;

      if (unresolvedScore >= config.scoreThreshold) {
        const [ymin, xmin, ymax, xmax] = boxes[i];
        const detection = {
          class: CLASS_INDICES.captcha_unresolved,
          className: 'captcha_unresolved',
          score: unresolvedScore,
          bbox: {
            ymin: ymin * height,
            xmin: xmin * width,
            ymax: ymax * height,
            xmax: xmax * width,
          },
          boundingBox: {
            x: xmin * width,
            y: ymin * height,
            width: (xmax - xmin) * width,
            height: (ymax - ymin) * height,
          },
        };
        detections.push(detection);
        stats.captcha_unresolved.count++;
        stats.captcha_unresolved.totalScore += unresolvedScore;

        // Check if this is the highest scoring detection so far
        if (!highestScoringDetection || unresolvedScore > highestScoringDetection.score) {
          highestScoringDetection = detection;
        }
      }

      // Check if we should include resolved detections
      if (resolvedScore >= config.scoreThreshold) {
        const [ymin, xmin, ymax, xmax] = boxes[i];
        const detection = {
          class: CLASS_INDICES.captcha_resolved,
          className: 'captcha_resolved',
          score: resolvedScore,
          bbox: {
            ymin: ymin * height,
            xmin: xmin * width,
            ymax: ymax * height,
            xmax: xmax * width,
          },
          boundingBox: {
            x: xmin * width,
            y: ymin * height,
            width: (xmax - xmin) * width,
            height: (ymax - ymin) * height,
          },
        };
        detections.push(detection);
        stats.captcha_resolved.count++;
        stats.captcha_resolved.totalScore += resolvedScore;

        // Check if this is the highest scoring detection so far
        if (!highestScoringDetection || resolvedScore > highestScoringDetection.score) {
          highestScoringDetection = detection;
        }
      }
    }

    // Calculate average scores
    if (stats.captcha_unresolved.count > 0) {
      stats.captcha_unresolved.avgScore =
        stats.captcha_unresolved.totalScore / stats.captcha_unresolved.count;
    }
    if (stats.captcha_resolved.count > 0) {
      stats.captcha_resolved.avgScore =
        stats.captcha_resolved.totalScore / stats.captcha_resolved.count;
    }

    const sortedUnresolved = [...allScores.captcha_unresolved].sort((a, b) => b - a);
    const sortedResolved = [...allScores.captcha_resolved].sort((a, b) => b - a);

    if (config.debug) {
      console.log('[TF Detector] ===== CAPTCHA DETECTION SUMMARY =====');
      console.log(
        `[TF Detector] Configuration: detectUnresolved=${config.detectUnresolved}, detectResolved=${config.detectResolved}, preferredClass=${config.preferredClass}`,
      );
      console.log(`[TF Detector] Score threshold: ${config.scoreThreshold}`);

      console.log('[TF Detector] ----- CAPTCHA_UNRESOLVED -----');
      console.log(`[TF Detector] Detections found: ${stats.captcha_unresolved.count}`);
      console.log(`[TF Detector] Max score: ${stats.captcha_unresolved.maxScore.toFixed(4)}`);
      console.log(
        `[TF Detector] Avg score (detected): ${stats.captcha_unresolved.avgScore.toFixed(4)}`,
      );
      console.log(
        `[TF Detector] Top 5 scores: [${sortedUnresolved
          .slice(0, 5)
          .map((s) => s.toFixed(4))
          .join(', ')}]`,
      );

      console.log('[TF Detector] ----- CAPTCHA_RESOLVED -----');
      console.log(`[TF Detector] Detections found: ${stats.captcha_resolved.count}`);
      console.log(`[TF Detector] Max score: ${stats.captcha_resolved.maxScore.toFixed(4)}`);
      console.log(
        `[TF Detector] Avg score (detected): ${stats.captcha_resolved.avgScore.toFixed(4)}`,
      );
      console.log(
        `[TF Detector] Top 5 scores: [${sortedResolved
          .slice(0, 5)
          .map((s) => s.toFixed(4))
          .join(', ')}]`,
      );

      console.log(`[TF Detector] Total valid captcha detections: ${detections.length}`);
      if (highestScoringDetection) {
        console.log(
          `[TF Detector] Highest scoring detection: ${highestScoringDetection.className} with score ${highestScoringDetection.score.toFixed(4)}`,
        );
      }
      console.log('[TF Detector] ===============================');
    }

    return {
      detections,
      highestScoringDetection,
      stats: {
        captcha_unresolved: {
          count: stats.captcha_unresolved.count,
          maxScore: stats.captcha_unresolved.maxScore,
          avgScore: stats.captcha_unresolved.avgScore,
        },
        captcha_resolved: {
          count: stats.captcha_resolved.count,
          maxScore: stats.captcha_resolved.maxScore,
          avgScore: stats.captcha_resolved.avgScore,
        },
        totalBoxes: numBoxes,
        totalDetections: detections.length,
      },
    };
  }

  /**
   * Take a screenshot and detect objects in it
   * @param {Object} dimensions - {width, height} of the image
   * @returns {Object[]} - Array of detection results
   */
  async function takeScreenshotAndDetect(dimensions) {
    if (window.browserController && typeof window.browserController.takeScreenshot === 'function') {
      const screenshotResult = await window.browserController.takeScreenshot();

      const screenshotBase64 = screenshotResult.data || screenshotResult;

      const imageBuffer = await base64ToRgbaBuffer(screenshotBase64, dimensions);

      return await window.tfCaptchaDetector.detectObjects(imageBuffer, dimensions);
    } else {
      throw new Error(
        `[kazeel][captcha-detector-tf] browserController is not available, cannot take screenshot`,
      );
    }
  }

  /**
   * Convert base64 image to RGBA buffer
   * @param {string} base64String - Base64-encoded image data
   * @param {Object} dimensions - {width, height} of the image
   */
  function base64ToRgbaBuffer(base64String, dimensions) {
    return new Promise((resolve) => {
      try {
        const img = new Image();

        // Create an in-memory canvas for image processing
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        img.onload = () => {
          const { width, height } = dimensions;
          canvas.width = width;
          canvas.height = height;

          // Draw the image onto the canvas
          ctx.drawImage(img, 0, 0, width, height);

          // Get the image data (RGBA)
          const imageData = ctx.getImageData(0, 0, width, height);

          if (config.debug) {
            console.log(`Converted base64 image to RGBA buffer (${width}x${height})`);
          }

          resolve(imageData.data);
        };

        img.onerror = (error) => {
          error(`Error while loading image from base64: ${error}`);
          resolve(null);
        };

        // Source data format: data:image/png;base64,BASE64DATA
        img.src = base64String.startsWith('data:')
          ? base64String
          : `data:image/png;base64,${base64String}`;
      } catch (error) {
        error(`Error in base64ToRgbaBuffer: ${error}`);
        resolve(null);
      }
    });
  }

  /**
   * Find the best bounding box for a captcha
   * @param {Object[]} detections - Array of detection results
   * @returns {Object|null} - The best bounding box or null if none found
   */
  function findBestCaptchaBoundingBox(detections) {
    if (!detections || detections.length === 0) {
      return null;
    }
    const sorted = [...detections].sort((a, b) => b.score - a.score);
    return sorted[0].boundingBox;
  }

  async function refreshBoundingBox({ width, height }) {
    console.log('[TF Detector] Refreshing bounding box...');
    const boundingBox = await getInitialBoundingBox({ width, height });

    if (window.screenCropper && typeof window.screenCropper.updateCropBox === 'function') {
      await window.screenCropper.updateCropBox(boundingBox);
    }
  }

  window.tfCaptchaDetector = {
    initialize,
    detectObjects,
    findBestCaptchaBoundingBox,
    takeScreenshotAndDetect,
    getInitialBoundingBox,
    refreshBoundingBox,
  };

  console.log('[TF Detector] TensorFlow detector script loaded');
})();
