/**
 * Test for error display component with SESSION_EXPIRED handling
 */

import { describe, it, expect } from 'vitest';
import { ErrorDisplay, LinkExpiredError } from '../../src/ui/components/error-display';
import { ProcessedError } from '../../src/common/error/types';

describe('ErrorDisplay Component', () => {
  it('should render LinkExpiredError when errorCode is SESSION_EXPIRED', () => {
    const sessionExpiredError: ProcessedError = {
      userMessage: 'Session has expired',
      shouldReplaceCard: true,
      errorCode: 'SESSION_EXPIRED',
      logLevel: 'error',
    };

    const result = ErrorDisplay(sessionExpiredError);

    // Should contain LinkExpiredError content
    expect(result.toString()).toContain('Link Expired');
    expect(result.toString()).toContain('The verification session has expired');
    expect(result.toString()).toContain('Try Again');
    expect(result.toString()).toContain('Contact Support');
  });

  it('should render CriticalError for other error codes with shouldReplaceCard', () => {
    const criticalError: ProcessedError = {
      userMessage: 'Browser connection failed',
      shouldReplaceCard: true,
      errorCode: 'BROWSER_CONNECTION_FAILED',
      logLevel: 'error',
    };

    const result = ErrorDisplay(criticalError);

    // Should contain CriticalError content
    expect(result.toString()).toContain('Connection Issue');
    expect(result.toString()).toContain('Browser connection failed');
    expect(result.toString()).not.toContain('Link Expired');
  });

  it('should render WarningBanner for warn level errors', () => {
    const warningError: ProcessedError = {
      userMessage: 'This is a warning',
      shouldReplaceCard: false,
      errorCode: 'BROWSER_CONNECTION_FAILED',
      logLevel: 'warn',
    };

    const result = ErrorDisplay(warningError);

    // Should contain WarningBanner content
    expect(result.toString()).toContain('bg-yellow-50');
    expect(result.toString()).toContain('This is a warning');
  });

  it('should prioritize SESSION_EXPIRED over shouldReplaceCard flag', () => {
    const sessionExpiredError: ProcessedError = {
      userMessage: 'Session has expired',
      shouldReplaceCard: false, // Even if this is false
      errorCode: 'SESSION_EXPIRED',
      logLevel: 'warn', // Even if this is warn
    };

    const result = ErrorDisplay(sessionExpiredError);

    // Should still render LinkExpiredError because errorCode takes priority
    expect(result.toString()).toContain('Link Expired');
    expect(result.toString()).toContain('The verification session has expired');
  });
});

describe('LinkExpiredError Component', () => {
  it('should render with correct content and styling', () => {
    const error: ProcessedError = {
      userMessage: 'Session expired',
      shouldReplaceCard: true,
      errorCode: 'SESSION_EXPIRED',
      logLevel: 'error',
    };

    const result = LinkExpiredError(error);

    expect(result.toString()).toContain('Link Expired');
    expect(result.toString()).toContain('The verification session has expired');
    expect(result.toString()).toContain('button-primary');
    expect(result.toString()).toContain('Try Again');
    expect(result.toString()).toContain('Contact Support');
    expect(result.toString()).toContain('https://kazeel.com/support');
  });
});
