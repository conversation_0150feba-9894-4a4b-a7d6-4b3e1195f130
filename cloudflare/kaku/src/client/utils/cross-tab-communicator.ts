/**
 * Cross-tab communication utilities for two-tab browser automation architecture
 * Supports BroadcastChannel API
 */

export interface CrossTabMessage {
  id: string;
  type: string;
  data?: any;
  timestamp: number;
}

export interface CrossTabResponse {
  id: string;
  type: 'response';
  result: any;
  timestamp: number;
}

export interface CrossTabCommunicationOptions {
  channelName: string;
  timeout?: number;
  debug?: boolean;
}

export class CrossTabCommunicationError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly source:
      | 'initialization'
      | 'communication'
      | 'timeout'
      | 'channel_closed' = 'communication',
  ) {
    super(`[kazeel][cross-tab-communicator] ${message}`);
    this.name = 'CrossTabCommunicationError';
  }
}

export class CrossTabCommunicator {
  private channel: typeof BroadcastChannel.prototype | null = null;
  private messageId = 0;
  private pendingMessages = new Map<
    string,
    { resolve: (value: any) => void; reject: (reason?: any) => void }
  >();
  private options: Required<CrossTabCommunicationOptions>;

  constructor(options: CrossTabCommunicationOptions) {
    this.options = {
      timeout: 10000,
      debug: false,
      ...options,
    };

    this.initializeCommunication();
  }

  private log(...args: any[]) {
    if (this.options.debug) {
      console.log('[kazeel][cross-tab-communicator]', ...args);
    }
  }

  private error(...args: any[]) {
    console.error('[kazeel][cross-tab-communicator]', ...args);
  }

  private async initializeCommunication(): Promise<void> {
    try {
      this.channel = new BroadcastChannel(this.options.channelName);
      this.channel.addEventListener('message', this.handleBroadcastMessage.bind(this));
      this.log('BroadcastChannel initialized successfully');
    } catch (err) {
      const error = new CrossTabCommunicationError(
        `BroadcastChannel initialization failed: ${err}`,
        'CHANNEL_INIT_FAILED',
        'initialization',
      );
      this.error(error.message, err);
      throw error;
    }
  }

  private handleBroadcastMessage(event: MessageEvent): void {
    // Type guard to ensure data is the expected format
    if (this.isValidMessage(event.data)) {
      this.handleMessage(event.data);
    }
  }

  private isValidMessage(data: any): data is CrossTabMessage | CrossTabResponse {
    return (
      data && typeof data === 'object' && 'id' in data && 'type' in data && 'timestamp' in data
    );
  }

  private handleMessage(data: CrossTabMessage | CrossTabResponse): void {
    this.log('Received message:', data);

    if (data.type === 'response' && this.pendingMessages.has(data.id)) {
      const { resolve, reject } = this.pendingMessages.get(data.id)!;
      this.pendingMessages.delete(data.id);

      const response = data as CrossTabResponse;
      if (response.result?.success !== false) {
        resolve(response.result);
      } else {
        const error = new CrossTabCommunicationError(
          response.result?.error || 'Cross-tab operation failed',
          'OPERATION_FAILED',
        );
        reject(error);
      }
    }
  }

  /**
   * Send a message to other tabs and wait for response
   */
  async sendMessage(type: string, data: any = {}): Promise<any> {
    if (!this.channel) {
      throw new CrossTabCommunicationError(
        'Communication channel not initialized',
        'CHANNEL_NOT_INITIALIZED',
      );
    }

    return new Promise((resolve, reject) => {
      const id = `msg_${++this.messageId}_${Date.now()}`;
      const message: CrossTabMessage = {
        id,
        type,
        data,
        timestamp: Date.now(),
      };

      // Store pending message
      this.pendingMessages.set(id, { resolve, reject });

      // Set timeout
      setTimeout(() => {
        if (this.pendingMessages.has(id)) {
          this.pendingMessages.delete(id);
          const error = new CrossTabCommunicationError(
            `Communication timeout for message type: ${type}`,
            'COMMUNICATION_TIMEOUT',
            'timeout',
          );
          reject(error);
        }
      }, this.options.timeout);

      // Send message
      try {
        this.sendMessageInternal(message);
      } catch (err) {
        this.pendingMessages.delete(id);
        const error = new CrossTabCommunicationError(
          `Failed to send message: ${err}`,
          'SEND_FAILED',
        );
        reject(error);
      }
    });
  }

  /**
   * Send a response to a received message
   */
  sendResponse(originalMessage: CrossTabMessage, result: any): void {
    const response: CrossTabResponse = {
      id: originalMessage.id,
      type: 'response',
      result,
      timestamp: Date.now(),
    };

    this.sendMessageInternal(response);
  }

  private sendMessageInternal(message: CrossTabMessage | CrossTabResponse): void {
    if (!this.channel) {
      throw new CrossTabCommunicationError(
        'Cannot send message: channel not initialized',
        'CHANNEL_NOT_INITIALIZED',
      );
    }

    this.log('Sending message:', message);

    try {
      this.channel.postMessage(message);
    } catch (err) {
      throw new CrossTabCommunicationError(`Failed to post message: ${err}`, 'POST_MESSAGE_FAILED');
    }
  }

  /**
   * Add a message handler for incoming messages
   */
  onMessage(handler: (message: CrossTabMessage) => Promise<any> | any): void {
    if (!this.channel) {
      throw new CrossTabCommunicationError(
        'Cannot add message handler: channel not initialized',
        'CHANNEL_NOT_INITIALIZED',
      );
    }

    const messageHandler = async (data: CrossTabMessage | CrossTabResponse) => {
      if (data.type !== 'response') {
        const message = data as CrossTabMessage;
        try {
          const result = await handler(message);
          this.sendResponse(message, result);
        } catch (error) {
          this.error('Message handler error:', error);
          this.sendResponse(message, {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }
    };

    this.channel.addEventListener('message', (event: MessageEvent) => {
      if (this.isValidMessage(event.data)) {
        messageHandler(event.data);
      }
    });
  }

  /**
   * Close the communication channel
   */
  close(): void {
    if (this.channel) {
      this.channel.close();
      this.channel = null;
    }

    // Reject all pending messages with proper error
    const closeError = new CrossTabCommunicationError(
      'Communication channel closed',
      'CHANNEL_CLOSED',
      'channel_closed',
    );

    for (const [, { reject }] of this.pendingMessages) {
      reject(closeError);
    }
    this.pendingMessages.clear();

    this.log('Communication channel closed');
  }
}
