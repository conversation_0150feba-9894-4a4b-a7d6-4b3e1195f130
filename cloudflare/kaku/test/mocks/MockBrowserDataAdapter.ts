import { <PERSON><PERSON> } from 'hono/utils/cookie';
import { BrowserDataAdapter } from '../../src/workflow/adapters/BrowserDataAdapter';

/**
 * Mock implementation of BrowserDataAdapter for testing
 * Provides simple in-memory storage without CDP complexity
 */
export class MockBrowserDataAdapter implements BrowserDataAdapter {
  private cookies: Cookie[] = [];
  private localStorage: Record<string, string | null> = {};
  private sessionStorage: Record<string, string | null> = {};

  constructor(
    initialCookies: Cookie[] = [],
    initialLocalStorage: Record<string, string | null> = {},
    initialSessionStorage: Record<string, string | null> = {},
  ) {
    this.cookies = [...initialCookies];
    this.localStorage = { ...initialLocalStorage };
    this.sessionStorage = { ...initialSessionStorage };
  }

  async getCookies(): Promise<Cookie[]> {
    return [...this.cookies];
  }

  async getLocalStorageData(): Promise<Record<string, string | null>> {
    return { ...this.localStorage };
  }

  async getSessionStorageData(): Promise<Record<string, string | null>> {
    return { ...this.sessionStorage };
  }

  async setCookies(cookies: Cookie[]): Promise<void> {
    this.cookies = [...cookies];
  }

  async setLocalStorageData(data: Record<string, string | null>): Promise<void> {
    this.localStorage = { ...data };
  }

  async setSessionStorageData(data: Record<string, string | null>): Promise<void> {
    this.sessionStorage = { ...data };
  }

  // Test utility methods
  updateCookies(cookies: Cookie[]): void {
    this.cookies = [...cookies];
  }

  updateLocalStorage(data: Record<string, string | null>): void {
    this.localStorage = { ...data };
  }

  updateSessionStorage(data: Record<string, string | null>): void {
    this.sessionStorage = { ...data };
  }

  reset(): void {
    this.cookies = [];
    this.localStorage = {};
    this.sessionStorage = {};
  }
}
