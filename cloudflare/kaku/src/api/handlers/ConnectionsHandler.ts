import { Hono } from 'hono';
import { <PERSON>kuApp } from '../../common/types';
import { platformDetails, PlatformTypes } from '../../ui/constants';
import { PLATFORM_ID, USER } from '../constants';
import { userAuthMiddleware } from '../user-auth-middleware';

const app = new Hono<KakuApp>();

app.post(`/:${PLATFORM_ID}/links`, userAuthMiddleware, async (c) => {
  try {
    const user = c.get(USER);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const platformId = c.req.param(PLATFORM_ID) as PlatformTypes;
    const userId = user.userId;

    const detail = platformDetails[platformId];
    if (!detail) {
      return c.json({ error: 'Invalid platformId' }, 400);
    }

    const coordinator = c.env.CoordinatorDO.idFromName(userId);
    const stub = c.env.CoordinatorDO.get(coordinator);
    const linkResponse = await stub.createLink(platformId, userId);
    return c.json(linkResponse);
  } catch (error) {
    console.error(`[API] Error creating link for platform ${c.req.param(PLATFORM_ID)}:`, error);
    return c.json(
      {
        error: error instanceof Error ? error.message : 'Internal Server Error',
      },
      500,
    );
  }
});

// /connections
app.get('/', userAuthMiddleware, async (c) => {
  try {
    const user = c.get(USER);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const coordinator = c.env.CoordinatorDO.idFromName(user.userId);
    const coordinatorStub = c.env.CoordinatorDO.get(coordinator);

    const connectionsResponse = await coordinatorStub.getPlatformConnections();
    return c.json(connectionsResponse);
  } catch (error) {
    return c.json({ error: 'Internal Server Error' }, 500);
  }
});

app.get(`/:${PLATFORM_ID}`, userAuthMiddleware, async (c) => {
  try {
    const user = c.get(USER);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const coordinator = c.env.CoordinatorDO.idFromName(user.userId);
    const coordinatorStub = c.env.CoordinatorDO.get(coordinator);

    const connectionsResponse = await coordinatorStub.getPlatformConnection(
      c.req.param(PLATFORM_ID) as PlatformTypes,
    );
    return c.json(connectionsResponse);
  } catch (error) {
    return c.json({ error: 'Internal Server Error' }, 500);
  }
});

// DELETE /connections/:serviceId - Disconnect from a platform
app.delete(`/:${PLATFORM_ID}`, userAuthMiddleware, async (c) => {
  try {
    const user = c.get(USER);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const serviceId = c.req.param(PLATFORM_ID) as PlatformTypes;

    const coordinator = c.env.CoordinatorDO.idFromName(user.userId);
    const coordinatorStub = c.env.CoordinatorDO.get(coordinator);

    const disconnected = await coordinatorStub.disconnectPlatform(serviceId);

    if (!disconnected) {
      return c.json({ error: 'Platform not found or not connected' }, 404);
    }

    return c.json({
      success: true,
      message: `Successfully disconnected from ${serviceId}`,
    });
  } catch (error) {
    console.error(`[API] Error disconnecting from ${c.req.param(PLATFORM_ID)}:`, error);
    return c.json(
      {
        error: error instanceof Error ? error.message : 'Internal Server Error',
      },
      500,
    );
  }
});

// DELETE /connections - Delete all connections for the authenticated user
app.delete('/', userAuthMiddleware, async (c) => {
  try {
    const user = c.get(USER);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }
    const coordinator = c.env.CoordinatorDO.idFromName(user.userId);
    const coordinatorStub = c.env.CoordinatorDO.get(coordinator);
    await coordinatorStub.deleteAllConnections();
    return c.newResponse(null, { status: 204 });
  } catch (error) {
    console.error('[API] Error deleting all connections:', error);
    return c.json(
      {
        error: error instanceof Error ? error.message : 'Internal Server Error',
      },
      500,
    );
  }
});

// Test endpoint to create links without authentication
// This endpoint is intended for testing purposes only
app.post(`/:${PLATFORM_ID}/test-links`, async (c) => {
  try {
    const platformId = c.req.param(PLATFORM_ID) as PlatformTypes;

    // Generate a test user ID for this request
    const testUserId = `test_user_${crypto.randomUUID()}`;

    const detail = platformDetails[platformId];
    if (!detail) {
      return c.json({ error: 'Invalid platformId' }, 400);
    }

    const coordinator = c.env.CoordinatorDO.idFromName(testUserId);
    const stub = c.env.CoordinatorDO.get(coordinator);
    const linkResponse = await stub.createLink(platformId, testUserId);

    return c.json({
      ...linkResponse,
      testUserId, // Include the test user ID in the response for reference
    });
  } catch (error) {
    console.error(
      `[API] Error creating test link for platform ${c.req.param(PLATFORM_ID)}:`,
      error,
    );
    return c.json(
      {
        error: error instanceof Error ? error.message : 'Internal Server Error',
      },
      500,
    );
  }
});

export { app as ConnectionsHandler };
